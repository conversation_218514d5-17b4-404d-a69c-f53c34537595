/**
 * 百度语音识别API测试脚本（修复版）
 * 严格按照百度官方示例进行测试
 */

const axios = require("axios");

// 百度API配置
const BAIDU_API_KEY = "4SJ6Uw96rKEEyeiuXLgrpaBi";
const BAIDU_SECRET_KEY = "WzrJdgyeL9iVBgFIgFqysuFcntzyQA5g";
const BAIDU_TOKEN_URL = "https://aip.baidubce.com/oauth/2.0/token";
const BAIDU_ASR_URL = "http://vop.baidu.com/server_api"; // 使用百度官方示例URL

/**
 * 获取百度访问令牌
 */
async function getBaiduAccessToken() {
  try {
    console.log("获取百度访问令牌...");

    const response = await axios({
      method: "POST",
      url: BAIDU_TOKEN_URL,
      params: {
        grant_type: "client_credentials",
        client_id: BAIDU_API_KEY,
        client_secret: BAIDU_SECRET_KEY,
      },
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
        Accept: "application/json",
      },
    });

    if (response.data && response.data.access_token) {
      console.log("✅ 成功获取访问令牌");
      return response.data.access_token;
    } else {
      throw new Error("获取访问令牌失败: 响应格式错误");
    }
  } catch (error) {
    console.error("❌ 获取访问令牌失败:", error.message);
    throw error;
  }
}

/**
 * 测试百度语音识别API（严格按照官方示例）
 */
async function testBaiduSpeechRecognition() {
  try {
    console.log("=== 百度语音识别API测试（修复版） ===\n");

    // 1. 获取访问令牌
    const token = await getBaiduAccessToken();
    console.log("访问令牌长度:", token.length);
    console.log("");

    // 2. 构建请求参数（严格按照百度官方示例）
    console.log("构建请求参数（百度官方格式）...");

    // 模拟PCM音频数据（3秒，16000Hz，16bit，单声道）
    const mockAudioDuration = 3; // 3秒
    const sampleRate = 16000; // 百度推荐采样率
    const bitDepth = 16; // 16bit位深
    const channels = 1; // 单声道

    // 计算原始PCM数据字节数：时长(秒) × 采样率(Hz) × 位深(字节) × 声道数
    const pcmDataLength =
      mockAudioDuration * sampleRate * (bitDepth / 8) * channels;

    // 生成模拟的PCM音频数据（16bit PCM格式的静音数据）
    const pcmBuffer = Buffer.alloc(pcmDataLength, 0); // 创建静音PCM数据
    const mockBase64Audio = pcmBuffer.toString("base64");

    // 严格按照百度官方示例构建请求参数
    const requestData = {
      format: "pcm", // string 必填：语音文件的格式，推荐pcm
      rate: sampleRate, // int 必填：采样率，16000、8000，固定值
      channel: channels, // int 必填：声道数，仅支持单声道，请填写固定值 1
      cuid: `test_device_${Date.now()}`, // string 必填：用户唯一标识，长度为60字符以内
      token: token, // string 必填：access_token鉴权信息
      speech: mockBase64Audio, // string 必填：base64编码的音频数据
      len: pcmDataLength, // int 必填：本地语音文件的字节数，单位字节
      dev_pid: 1537, // int 选填：识别模型，1537为普通话，1737为英语
    };

    console.log("请求参数:");
    console.log("- format:", requestData.format);
    console.log("- rate:", requestData.rate);
    console.log("- channel:", requestData.channel);
    console.log("- cuid:", requestData.cuid);
    console.log("- len:", requestData.len, "字节");
    console.log("- dev_pid:", requestData.dev_pid);
    console.log("- speech长度:", requestData.speech.length, "字符");
    console.log("- token长度:", requestData.token.length, "字符");
    console.log("");

    // 3. 发送请求（严格按照百度官方示例）
    console.log("发送请求到百度语音识别API...");
    console.log("URL:", BAIDU_ASR_URL);
    console.log("Method: POST");
    console.log("Content-Type: application/json");
    console.log("");

    const options = {
      method: "POST",
      url: BAIDU_ASR_URL,
      headers: {
        "Content-Type": "application/json", // 百度官方要求的固定头部
      },
      data: requestData,
    };

    const response = await axios(options);

    console.log("=== API响应 ===");
    console.log("状态码:", response.status);
    console.log("响应数据:", JSON.stringify(response.data, null, 2));

    // 4. 处理响应
    if (response.data && response.data.err_no === 0) {
      console.log("✅ 请求成功！");
      if (response.data.result && response.data.result.length > 0) {
        console.log("识别结果:", response.data.result[0]);
      } else {
        console.log("⚠️ 识别成功但无结果（可能是模拟数据导致）");
      }
    } else if (response.data && response.data.err_no !== 0) {
      console.log("❌ API返回错误:");
      console.log("错误码:", response.data.err_no);
      console.log("错误信息:", response.data.err_msg);

      // 常见错误码说明
      const errorCodes = {
        3300: "语音数据格式错误",
        3301: "识别超时",
        3302: "验证失败",
        3303: "语音服务器后端错误",
        3304: "用户请求超限",
        3305: "用户请求频率超限",
        3307: "语音识别失败",
      };

      if (errorCodes[response.data.err_no]) {
        console.log("错误说明:", errorCodes[response.data.err_no]);
      }
    } else {
      console.log("❌ 未知响应格式");
    }
  } catch (error) {
    console.error("❌ 测试失败:", error.message);

    if (error.response) {
      console.error("HTTP状态码:", error.response.status);
      console.error("响应数据:", error.response.data);
    }
  }
}

/**
 * 验证参数格式
 */
function validateParameters() {
  console.log("=== 参数验证 ===");
  console.log("✅ API Key长度:", BAIDU_API_KEY.length);
  console.log("✅ Secret Key长度:", BAIDU_SECRET_KEY.length);
  console.log("✅ Token URL:", BAIDU_TOKEN_URL);
  console.log("✅ ASR URL:", BAIDU_ASR_URL);
  console.log("");
}

// 运行测试
async function main() {
  validateParameters();
  await testBaiduSpeechRecognition();
}

main().catch(console.error);
