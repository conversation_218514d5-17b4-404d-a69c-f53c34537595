import AsyncStorage from '@react-native-async-storage/async-storage';
import { Message, LLMService, LLMProvider } from './llm';
import axios from 'axios';

// 对话类型定义
export interface Conversation {
  id: string;
  title: string;
  role: string; // 系统提示词/角色描述
  messages: ChatMessage[];
  createdAt: string;
  updatedAt: string;
  summary?: string; // 对话摘要
  lastMessagePreview?: string; // 最后一条消息预览
  totalMessages: number; // 消息总数
  pinnedAt?: string; // 置顶时间（如果有）
}

// 聊天消息类型定义
export interface ChatMessage {
  id: string;
  text: string;
  isUser: boolean; // true为用户消息，false为AI消息
  timestamp: string;
  role?: 'system' | 'user' | 'assistant'; // 消息角色
  status?: 'sending' | 'sent' | 'error'; // 消息状态
}

// 分页选项
export interface PaginationOptions {
  page: number;
  limit: number;
}

// 存储键前缀
const STORAGE_KEY_PREFIX = 'conversation_';

/**
 * 对话管理服务类
 */
export class ConversationService {
  private llmService: LLMService;

  /**
   * 构造函数
   */
  constructor() {
    // 导入hasValidApiKey函数
    const { hasValidApiKey } = require('./api');

    // 根据可用的API密钥选择提供商
    let provider = LLMProvider.DeepSeek; // 默认使用DeepSeek
    let model = 'deepseek-chat';

    // 如果DeepSeek API密钥无效但OpenAI API密钥有效，使用OpenAI
    if (!hasValidApiKey('deepseek') && hasValidApiKey('openai')) {
      provider = LLMProvider.OpenAI;
      model = 'gpt-3.5-turbo';
    }

    this.llmService = new LLMService({
      provider,
      model,
      temperature: 0.7,
      maxTokens: 1000
    });
  }

  /**
   * 预设模型身份模板
   */
  readonly roleTemplates = [
    {
      id: 'teacher',
      name: '英语教师',
      description: 'You are an English speaker and a professional English teacher. You are patient, encouraging, and good at explaining grammar rules and vocabulary. You should correct the user\'s English mistakes in a helpful way.',
    },
    {
      id: 'travel_guide',
      name: '旅游向导',
      description: 'You are an English speaker and an experienced travel guide. You know about popular tourist destinations, travel tips, and cultural customs. You can help the user practice travel-related conversations.',
    },
    {
      id: 'business_expert',
      name: '商务专家',
      description: 'You are an English speaker and a business professional. You are familiar with business terminology, negotiation skills, and professional etiquette. You can help the user practice business English.',
    },
    {
      id: 'casual_friend',
      name: '日常朋友',
      description: 'You are an English speaker and a friendly conversation partner. You enjoy casual chats about daily life, hobbies, movies, music, and other common topics. You use natural, everyday English expressions.',
    },
    {
      id: 'debate_partner',
      name: '辩论伙伴',
      description: 'You are an English speaker and a skilled debate partner. You can discuss complex topics, present arguments, and challenge the user\'s opinions respectfully to help them develop critical thinking in English.',
    },
  ];

  /**
   * 创建新对话
   * @param role 系统提示词/角色描述
   * @param title 对话标题（可选）
   * @returns Promise<Conversation> 新创建的对话
   */
  async createConversation(role: string, title?: string): Promise<Conversation> {
    const now = new Date().toISOString();
    // 使用时间戳 + 随机字符串确保ID唯一
    const id = `${Date.now()}-${Math.random().toString(36).substring(2, 10)}`;

    // 确保角色描述包含基础语句
    const validatedRole = this.validateRoleDescription(role);

    // 生成默认标题
    const defaultTitle = title || this.generateDefaultTitle(validatedRole);

    const conversation: Conversation = {
      id,
      title: defaultTitle,
      role: validatedRole,
      messages: [],
      createdAt: now,
      updatedAt: now,
      summary: '',
      lastMessagePreview: '',
      totalMessages: 0
    };

    // 保存到本地存储
    await this.saveConversation(conversation);

    return conversation;
  }

  /**
   * 验证角色描述，确保包含基础语句
   * @param role 角色描述
   * @returns string 验证后的角色描述
   */
  validateRoleDescription(role: string): string {
    const baseStatement = 'You are an English speaker';

    // 如果角色描述为空，返回基础语句
    if (!role || role.trim() === '') {
      return `${baseStatement} and a friendly conversation partner.`;
    }

    // 检查是否已包含基础语句（不区分大小写）
    if (!role.toLowerCase().includes(baseStatement.toLowerCase())) {
      // 如果以"you are"开头但不包含"English speaker"
      if (role.toLowerCase().startsWith('you are')) {
        return role.replace(/you are/i, baseStatement);
      }
      // 如果不以"you are"开头
      return `${baseStatement} and ${role}`;
    }

    return role;
  }

  /**
   * 根据角色描述生成默认标题
   * @param role 角色描述
   * @returns string 默认标题
   */
  private generateDefaultTitle(role: string): string {
    // 从角色描述中提取关键词
    const keywords = role.split(' ')
      .filter(word => word.length > 3) // 只保留长度大于3的单词
      .slice(0, 3); // 最多取3个单词

    if (keywords.length > 0) {
      // 使用关键词生成标题
      return `与${keywords.join(' ')}的对话`;
    } else {
      // 使用时间戳生成标题，添加随机字符确保唯一性
      const timestamp = Date.now().toString().slice(-4);
      const randomChars = Math.random().toString(36).substring(2, 5);
      return `对话 ${timestamp}-${randomChars}`;
    }
  }

  /**
   * 获取所有对话
   * @param options 排序和过滤选项
   * @returns Promise<Conversation[]> 所有对话
   */
  async getAllConversations(options?: {
    sortBy?: 'updatedAt' | 'createdAt' | 'pinnedAt';
    search?: string;
  }): Promise<Conversation[]> {
    try {
      // 获取所有键
      const keys = await AsyncStorage.getAllKeys();
      const conversationKeys = keys.filter(key => key.startsWith(STORAGE_KEY_PREFIX));

      // 获取所有对话
      const conversations: Conversation[] = [];
      for (const key of conversationKeys) {
        const json = await AsyncStorage.getItem(key);
        if (json) {
          conversations.push(JSON.parse(json));
        }
      }

      // 过滤（如果有搜索条件）
      let filteredConversations = conversations;
      if (options?.search) {
        const searchTerm = options.search.toLowerCase();
        filteredConversations = conversations.filter(conv =>
          conv.title.toLowerCase().includes(searchTerm) ||
          conv.summary?.toLowerCase().includes(searchTerm) ||
          conv.lastMessagePreview?.toLowerCase().includes(searchTerm)
        );
      }

      // 排序
      const sortBy = options?.sortBy || 'updatedAt';

      // 先按置顶状态排序，再按指定字段排序
      return filteredConversations.sort((a, b) => {
        // 置顶的对话始终在前
        if (a.pinnedAt && !b.pinnedAt) return -1;
        if (!a.pinnedAt && b.pinnedAt) return 1;
        if (a.pinnedAt && b.pinnedAt) {
          // 如果都置顶，按置顶时间排序
          return new Date(b.pinnedAt).getTime() - new Date(a.pinnedAt).getTime();
        }

        // 非置顶对话按指定字段排序
        return new Date(b[sortBy] || b.updatedAt).getTime() -
               new Date(a[sortBy] || a.updatedAt).getTime();
      });
    } catch (error) {
      console.error('获取对话列表失败:', error);
      return [];
    }
  }

  /**
   * 分页获取对话列表
   * @param options 分页选项
   * @returns Promise<Conversation[]> 分页后的对话列表
   */
  async getConversationsPaginated(options: PaginationOptions & {
    sortBy?: 'updatedAt' | 'createdAt' | 'pinnedAt';
    search?: string;
  }): Promise<Conversation[]> {
    const { page = 0, limit = 10, sortBy, search } = options;

    // 获取所有对话
    const allConversations = await this.getAllConversations({ sortBy, search });

    // 计算分页
    const start = page * limit;
    const end = start + limit;

    // 返回分页结果
    return allConversations.slice(start, end);
  }

  /**
   * 获取单个对话
   * @param id 对话ID
   * @param loadMessages 是否加载消息（默认加载）
   * @returns Promise<Conversation | null> 对话或null
   */
  async getConversation(
    id: string,
    loadMessages: boolean = true
  ): Promise<Conversation | null> {
    try {
      const json = await AsyncStorage.getItem(`${STORAGE_KEY_PREFIX}${id}`);
      if (!json) return null;

      const conversation: Conversation = JSON.parse(json);

      // 如果不需要加载消息，返回空消息数组
      if (!loadMessages) {
        conversation.messages = [];
      }

      return conversation;
    } catch (error) {
      console.error(`获取对话 ${id} 失败:`, error);
      return null;
    }
  }

  /**
   * 分页获取对话消息
   * @param id 对话ID
   * @param options 分页选项
   * @returns Promise<ChatMessage[]> 分页后的消息列表
   */
  async getMessagesPaginated(
    id: string,
    options: PaginationOptions
  ): Promise<ChatMessage[]> {
    const { page = 0, limit = 20 } = options;

    // 获取对话
    const conversation = await this.getConversation(id);
    if (!conversation) return [];

    // 计算分页
    const totalMessages = conversation.messages.length;
    const start = Math.max(0, totalMessages - (page + 1) * limit);
    const end = Math.max(0, totalMessages - page * limit);

    // 返回分页结果（最新的消息在后面）
    return conversation.messages.slice(start, end);
  }

  /**
   * 保存对话
   * @param conversation 对话
   * @param updateMetadata 是否更新元数据（默认更新）
   * @returns Promise<void>
   */
  async saveConversation(
    conversation: Conversation,
    updateMetadata: boolean = true
  ): Promise<void> {
    try {
      if (updateMetadata) {
        // 更新时间戳
        conversation.updatedAt = new Date().toISOString();

        // 更新消息总数
        conversation.totalMessages = conversation.messages.length;

        // 更新最后一条消息预览
        if (conversation.messages.length > 0) {
          const lastMessage = conversation.messages[conversation.messages.length - 1];
          conversation.lastMessagePreview = this.truncateText(lastMessage.text, 50);
        }
      }

      // 保存到本地存储
      await AsyncStorage.setItem(
        `${STORAGE_KEY_PREFIX}${conversation.id}`,
        JSON.stringify(conversation)
      );
    } catch (error) {
      console.error(`保存对话 ${conversation.id} 失败:`, error);
      throw error;
    }
  }

  /**
   * 截断文本
   * @param text 原文本
   * @param maxLength 最大长度
   * @returns string 截断后的文本
   */
  private truncateText(text: string, maxLength: number): string {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  }

  /**
   * 删除对话
   * @param id 对话ID
   * @returns Promise<void>
   */
  async deleteConversation(id: string): Promise<void> {
    try {
      await AsyncStorage.removeItem(`${STORAGE_KEY_PREFIX}${id}`);
    } catch (error) {
      console.error(`删除对话 ${id} 失败:`, error);
      throw error;
    }
  }

  /**
   * 清空所有对话
   * @returns Promise<void>
   */
  async clearAllConversations(): Promise<void> {
    try {
      const keys = await AsyncStorage.getAllKeys();
      const conversationKeys = keys.filter(key => key.startsWith(STORAGE_KEY_PREFIX));

      if (conversationKeys.length > 0) {
        await AsyncStorage.multiRemove(conversationKeys);
      }
    } catch (error) {
      console.error('清空所有对话失败:', error);
      throw error;
    }
  }

  /**
   * 更新对话标题
   * @param id 对话ID
   * @param newTitle 新标题
   * @returns Promise<Conversation | null> 更新后的对话或null
   */
  async updateConversationTitle(id: string, newTitle: string): Promise<Conversation | null> {
    try {
      const conversation = await this.getConversation(id);
      if (!conversation) return null;

      conversation.title = newTitle;
      await this.saveConversation(conversation);

      return conversation;
    } catch (error) {
      console.error(`更新对话标题失败:`, error);
      throw error;
    }
  }

  /**
   * 更新对话的模型身份
   * @param id 对话ID
   * @param newRole 新的模型身份描述
   * @returns Promise<Conversation | null> 更新后的对话或null
   */
  async updateConversationRole(id: string, newRole: string): Promise<Conversation | null> {
    try {
      const conversation = await this.getConversation(id);
      if (!conversation) return null;

      // 验证角色描述
      const validatedRole = this.validateRoleDescription(newRole);

      // 更新角色描述
      conversation.role = validatedRole;

      // 添加系统消息，提示角色已更改
      await this.addMessage(
        id,
        `[系统消息] 模型身份已更改为: "${this.truncateText(validatedRole, 50)}"`,
        false
      );

      // 保存对话
      await this.saveConversation(conversation);

      return conversation;
    } catch (error) {
      console.error(`更新对话模型身份失败:`, error);
      throw error;
    }
  }

  /**
   * 切换对话置顶状态
   * @param id 对话ID
   * @returns Promise<Conversation | null> 更新后的对话或null
   */
  async toggleConversationPin(id: string): Promise<Conversation | null> {
    try {
      const conversation = await this.getConversation(id);
      if (!conversation) return null;

      if (conversation.pinnedAt) {
        // 取消置顶
        conversation.pinnedAt = undefined;
      } else {
        // 置顶
        conversation.pinnedAt = new Date().toISOString();
      }

      await this.saveConversation(conversation);
      return conversation;
    } catch (error) {
      console.error(`切换对话置顶状态失败:`, error);
      throw error;
    }
  }

  /**
   * 添加消息到对话
   * @param conversationId 对话ID
   * @param text 消息文本
   * @param isUser 是否为用户消息
   * @param status 消息状态（可选）
   * @returns Promise<ChatMessage> 添加的消息
   */
  async addMessage(
    conversationId: string,
    text: string,
    isUser: boolean,
    status: 'sending' | 'sent' | 'error' = 'sent'
  ): Promise<ChatMessage> {
    // 获取对话
    const conversation = await this.getConversation(conversationId);
    if (!conversation) {
      throw new Error(`对话 ${conversationId} 不存在`);
    }

    // 创建新消息，确保ID唯一
    const message: ChatMessage = {
      // 使用时间戳 + 随机字符串确保ID唯一
      id: `${Date.now()}-${Math.random().toString(36).substring(2, 10)}`,
      text,
      isUser,
      timestamp: new Date().toLocaleTimeString(),
      role: isUser ? 'user' : 'assistant',
      status
    };

    // 添加到对话
    conversation.messages.push(message);

    // 如果消息数量超过限制，删除最早的消息
    const MAX_MESSAGES = 200; // 最多保存200条消息
    if (conversation.messages.length > MAX_MESSAGES) {
      conversation.messages = conversation.messages.slice(-MAX_MESSAGES);
    }

    // 保存对话
    await this.saveConversation(conversation);

    return message;
  }

  /**
   * 更新消息状态
   * @param conversationId 对话ID
   * @param messageId 消息ID
   * @param status 新状态
   * @returns Promise<ChatMessage | null> 更新后的消息或null
   */
  async updateMessageStatus(
    conversationId: string,
    messageId: string,
    status: 'sending' | 'sent' | 'error'
  ): Promise<ChatMessage | null> {
    // 获取对话
    const conversation = await this.getConversation(conversationId);
    if (!conversation) return null;

    // 查找消息
    const messageIndex = conversation.messages.findIndex(msg => msg.id === messageId);
    if (messageIndex === -1) return null;

    // 更新状态
    conversation.messages[messageIndex].status = status;

    // 保存对话
    await this.saveConversation(conversation, false);

    return conversation.messages[messageIndex];
  }

  /**
   * 发送消息到LLM并获取回复
   * @param conversationId 对话ID
   * @param userMessage 用户消息
   * @param useMock 是否使用模拟数据（用于测试）
   * @returns Promise<ChatMessage> AI回复消息
   */
  async sendMessageToLLM(
    conversationId: string,
    userMessage: string,
    useMock: boolean = false  // 禁用模拟数据，直接使用API
  ): Promise<ChatMessage> {
    // 获取对话
    const conversation = await this.getConversation(conversationId);
    if (!conversation) {
      throw new Error(`对话 ${conversationId} 不存在`);
    }

    // 添加用户消息（状态为发送中）
    const userChatMessage = await this.addMessage(conversationId, userMessage, true, 'sending');

    try {
      // 更新用户消息状态为已发送
      await this.updateMessageStatus(conversationId, userChatMessage.id, 'sent');

      // 构建LLM消息历史
      const messages: Message[] = [
        // 系统提示词
        {
          role: 'system',
          content: conversation.role
        }
      ];

      // 添加历史消息（最多取最近50条）
      const historyMessages = conversation.messages
        .slice(-50) // 最多取最近50条消息
        .filter(msg => msg.id !== userChatMessage.id); // 排除刚刚添加的用户消息

      for (const msg of historyMessages) {
        // 只添加状态为已发送的消息
        if (msg.status !== 'error') {
          messages.push({
            role: msg.isUser ? 'user' : 'assistant',
            content: msg.text
          });
        }
      }

      // 添加当前用户消息
      messages.push({
        role: 'user',
        content: userMessage
      });

      // 发送到LLM
      let aiResponse: string;
      // 即使useMock为true，也强制使用真实API
      aiResponse = await this.llmService.sendMessage(messages);

      // 添加AI回复
      const aiMessage = await this.addMessage(conversationId, aiResponse, false);

      // 如果对话没有摘要，尝试生成摘要
      if (!conversation.summary && conversation.messages.length >= 5) {
        await this.generateConversationSummary(conversationId, useMock);
      }

      return aiMessage;
    } catch (error) {
      // 更新用户消息状态为错误
      await this.updateMessageStatus(conversationId, userChatMessage.id, 'error');

      // 添加详细的错误消息
      console.error('发送消息到LLM失败:', error);

      // 构建详细的错误信息
      let errorMessage = '';

      if (axios.isAxiosError(error) && error.response) {
        // API错误
        const statusCode = error.response.status;
        const errorDetails = error.response.data?.error?.message || 'Unknown API error';

        if (statusCode === 402) {
          errorMessage = `[API错误] DeepSeek API余额不足 (402): ${errorDetails}。请充值您的DeepSeek账户或联系管理员。`;
        } else if (statusCode === 401) {
          errorMessage = `[API错误] 认证失败 (401): ${errorDetails}。请检查您的API密钥是否有效。`;
        } else if (statusCode === 429) {
          errorMessage = `[API错误] 请求频率超限 (429): ${errorDetails}。请稍后再试。`;
        } else {
          errorMessage = `[API错误] (${statusCode}): ${errorDetails}`;
        }
      } else {
        // 其他错误
        errorMessage = `[系统错误] ${(error as Error).message || '与AI服务通信失败，请稍后再试'}`;
      }

      // 添加错误消息作为AI回复
      return await this.addMessage(conversationId, errorMessage, false, 'error');
    }
  }

  /**
   * 生成对话摘要
   * @param conversationId 对话ID
   * @param useMock 是否使用模拟数据
   * @returns Promise<string | null> 生成的摘要或null
   */
  private async generateConversationSummary(
    conversationId: string,
    useMock: boolean = false
  ): Promise<string | null> {
    try {
      const conversation = await this.getConversation(conversationId);
      if (!conversation) return null;

      // 构建摘要请求
      const messages: Message[] = [
        {
          role: 'system',
          content: '你是一个助手，请根据对话内容生成一个简短的摘要（不超过30个字），用于描述这个对话的主题。'
        }
      ];

      // 添加最近的5条消息作为上下文
      const recentMessages = conversation.messages.slice(-5);
      for (const msg of recentMessages) {
        messages.push({
          role: msg.isUser ? 'user' : 'assistant',
          content: msg.text
        });
      }

      // 添加摘要请求
      messages.push({
        role: 'user',
        content: '请根据我们的对话，生成一个简短的摘要（不超过30个字），用于描述这个对话的主题。'
      });

      // 发送到LLM
      let summary: string;
      try {
        // 始终使用真实API
        summary = await this.llmService.sendMessage(messages);
      } catch (error) {
        console.error('生成摘要时API错误:', error);
        // 如果API调用失败，使用默认摘要
        summary = '英语对话';
      }

      // 清理摘要（去除引号等）
      summary = summary.replace(/["'"：:]/g, '').trim();
      if (summary.length > 30) {
        summary = summary.substring(0, 30) + '...';
      }

      // 更新对话摘要
      conversation.summary = summary;
      await this.saveConversation(conversation, false);

      return summary;
    } catch (error) {
      console.error('生成对话摘要失败:', error);
      return null;
    }
  }

  /**
   * 设置LLM提供商
   * @param provider LLM提供商
   */
  setLLMProvider(provider: LLMProvider): void {
    this.llmService.setConfig({ provider });
  }

  /**
   * 设置LLM模型
   * @param model 模型名称
   */
  setLLMModel(model: string): void {
    this.llmService.setConfig({ model });
  }
}
