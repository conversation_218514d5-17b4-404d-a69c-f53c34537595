/**
 * 测试PCM数据提取功能
 * 验证从WAV文件中正确提取PCM数据
 */

const fs = require('fs');
const path = require('path');

/**
 * 分析WAV文件头
 */
function analyzeWavFile(buffer) {
  console.log('=== WAV文件分析 ===');
  console.log(`文件大小: ${buffer.length}字节`);
  
  // 检查RIFF签名
  const riffSignature = buffer.toString('ascii', 0, 4);
  const waveSignature = buffer.toString('ascii', 8, 12);
  
  console.log(`RIFF签名: "${riffSignature}"`);
  console.log(`WAVE签名: "${waveSignature}"`);
  
  if (riffSignature !== 'RIFF' || waveSignature !== 'WAVE') {
    console.log('❌ 不是有效的WAV文件');
    return null;
  }
  
  // 读取文件大小
  const fileSize = buffer.readUInt32LE(4) + 8;
  console.log(`RIFF文件大小: ${fileSize}字节`);
  
  // 查找fmt和data chunk
  let offset = 12;
  let dataChunkOffset = 0;
  let dataChunkSize = 0;
  let fmtChunkFound = false;
  
  while (offset < buffer.length - 8) {
    const chunkId = buffer.toString('ascii', offset, offset + 4);
    const chunkSize = buffer.readUInt32LE(offset + 4);
    
    console.log(`发现chunk: "${chunkId}", 大小: ${chunkSize}字节, 偏移: ${offset}`);
    
    if (chunkId === 'fmt ') {
      fmtChunkFound = true;
      // 分析fmt chunk
      const audioFormat = buffer.readUInt16LE(offset + 8);
      const numChannels = buffer.readUInt16LE(offset + 10);
      const sampleRate = buffer.readUInt32LE(offset + 12);
      const bitsPerSample = buffer.readUInt16LE(offset + 22);
      
      console.log(`音频格式: ${audioFormat} (1=PCM)`);
      console.log(`声道数: ${numChannels}`);
      console.log(`采样率: ${sampleRate}Hz`);
      console.log(`位深: ${bitsPerSample}bit`);
    } else if (chunkId === 'data') {
      dataChunkOffset = offset + 8;
      dataChunkSize = chunkSize;
      console.log(`✅ 找到data chunk: 偏移=${dataChunkOffset}, 大小=${dataChunkSize}字节`);
      break;
    }
    
    offset += 8 + chunkSize;
    
    // 防止无限循环
    if (offset >= buffer.length) {
      break;
    }
  }
  
  return {
    headerSize: dataChunkOffset,
    pcmDataSize: dataChunkSize,
    fmtChunkFound
  };
}

/**
 * 创建模拟WAV文件进行测试
 */
function createMockWavFile() {
  console.log('=== 创建模拟WAV文件 ===');
  
  // WAV文件头（44字节）
  const header = Buffer.alloc(44);
  
  // RIFF header
  header.write('RIFF', 0);
  header.writeUInt32LE(36 + 1000, 4); // 文件大小 - 8
  header.write('WAVE', 8);
  
  // fmt chunk
  header.write('fmt ', 12);
  header.writeUInt32LE(16, 16); // fmt chunk size
  header.writeUInt16LE(1, 20);  // audio format (PCM)
  header.writeUInt16LE(1, 22);  // num channels
  header.writeUInt32LE(16000, 24); // sample rate
  header.writeUInt32LE(32000, 28); // byte rate
  header.writeUInt16LE(2, 32);  // block align
  header.writeUInt16LE(16, 34); // bits per sample
  
  // data chunk
  header.write('data', 36);
  header.writeUInt32LE(1000, 40); // data size
  
  // PCM数据（1000字节的静音）
  const pcmData = Buffer.alloc(1000, 0);
  
  // 合并文件头和PCM数据
  const wavFile = Buffer.concat([header, pcmData]);
  
  console.log(`创建的WAV文件大小: ${wavFile.length}字节`);
  console.log(`文件头大小: ${header.length}字节`);
  console.log(`PCM数据大小: ${pcmData.length}字节`);
  
  return wavFile;
}

/**
 * 测试PCM数据提取
 */
function testPcmExtraction() {
  console.log('=== 测试PCM数据提取 ===\n');
  
  // 创建模拟WAV文件
  const wavFile = createMockWavFile();
  console.log('');
  
  // 分析WAV文件
  const analysis = analyzeWavFile(wavFile);
  console.log('');
  
  if (analysis) {
    console.log('=== 提取结果 ===');
    console.log(`文件头大小: ${analysis.headerSize}字节`);
    console.log(`PCM数据大小: ${analysis.pcmDataSize}字节`);
    console.log(`fmt chunk: ${analysis.fmtChunkFound ? '找到' : '未找到'}`);
    
    // 提取PCM数据
    const pcmData = wavFile.slice(analysis.headerSize, analysis.headerSize + analysis.pcmDataSize);
    console.log(`实际提取的PCM数据大小: ${pcmData.length}字节`);
    
    // 转换为base64
    const fullBase64 = wavFile.toString('base64');
    const headerSizeInBase64 = Math.ceil(analysis.headerSize * 4 / 3);
    const pcmBase64 = fullBase64.substring(headerSizeInBase64);
    
    console.log('');
    console.log('=== Base64转换 ===');
    console.log(`完整文件base64长度: ${fullBase64.length}`);
    console.log(`文件头在base64中的大小: ${headerSizeInBase64}`);
    console.log(`PCM数据base64长度: ${pcmBase64.length}`);
    console.log(`期望的PCM base64长度: ${Math.ceil(analysis.pcmDataSize * 4 / 3)}`);
    
    // 验证提取的正确性
    const extractedPcmSize = Math.floor(pcmBase64.length * 3 / 4);
    console.log(`从base64反推的PCM大小: ${extractedPcmSize}字节`);
    
    if (Math.abs(extractedPcmSize - analysis.pcmDataSize) <= 2) {
      console.log('✅ PCM数据提取成功！');
    } else {
      console.log('❌ PCM数据提取有误差');
    }
  } else {
    console.log('❌ WAV文件分析失败');
  }
}

// 运行测试
testPcmExtraction();
