import { ApiConfigService } from './apiConfig';
import { SpeechRecognitionService } from './api';

/**
 * API诊断工具
 * 用于检测和诊断API配置和连接问题
 */
export class ApiDiagnostics {
  
  /**
   * 完整的API配置诊断
   * @returns Promise<{success: boolean, report: string}> 诊断报告
   */
  static async runFullDiagnostics(): Promise<{success: boolean, report: string}> {
    const report: string[] = [];
    let success = true;

    report.push('=== API配置诊断报告 ===');
    report.push(`诊断时间: ${new Date().toLocaleString()}`);
    report.push('');

    try {
      // 1. 检查配置服务
      report.push('1. 检查API配置服务...');
      const configStatus = await ApiConfigService.getConfigStatus();
      report.push(`   配置来源: ${configStatus.configSource}`);
      report.push(`   是否有用户配置: ${configStatus.hasUserConfig}`);
      report.push(`   是否使用默认配置: ${configStatus.isUsingDefault}`);

      // 2. 检查API密钥
      report.push('');
      report.push('2. 检查API密钥...');
      const config = await ApiConfigService.getApiConfig();
      
      // DeepSeek API Key检查
      if (config.deepseekApiKey && config.deepseekApiKey.startsWith('sk-')) {
        report.push(`   ✓ DeepSeek API Key: 格式正确 (${config.deepseekApiKey.substring(0, 8)}...)`);
      } else {
        report.push(`   ✗ DeepSeek API Key: 格式错误或为空`);
        success = false;
      }

      // 百度API Key检查
      if (config.baiduApiKey && config.baiduApiKey.length >= 10) {
        report.push(`   ✓ 百度API Key: 格式正确 (${config.baiduApiKey.substring(0, 8)}... 长度:${config.baiduApiKey.length})`);
      } else {
        report.push(`   ✗ 百度API Key: 格式错误或为空`);
        success = false;
      }

      // 百度Secret Key检查
      if (config.baiduSecretKey && config.baiduSecretKey.length >= 10) {
        report.push(`   ✓ 百度Secret Key: 格式正确 (${config.baiduSecretKey.substring(0, 8)}... 长度:${config.baiduSecretKey.length})`);
      } else {
        report.push(`   ✗ 百度Secret Key: 格式错误或为空`);
        success = false;
      }

      // 3. 测试百度访问令牌获取
      report.push('');
      report.push('3. 测试百度访问令牌获取...');
      
      try {
        // 先清除缓存，确保使用最新配置
        SpeechRecognitionService.clearBaiduTokenCache();
        report.push('   已清除令牌缓存');
        
        const token = await SpeechRecognitionService.getBaiduAccessToken();
        if (token && token.length > 0) {
          report.push(`   ✓ 成功获取访问令牌 (${token.substring(0, 20)}... 长度:${token.length})`);
        } else {
          report.push(`   ✗ 获取访问令牌失败: 令牌为空`);
          success = false;
        }
      } catch (error) {
        report.push(`   ✗ 获取访问令牌失败: ${error}`);
        success = false;
      }

      // 4. 检查缓存状态
      report.push('');
      report.push('4. 检查缓存状态...');
      const cachedConfig = ApiConfigService.getCachedConfig();
      if (cachedConfig) {
        report.push('   ✓ 配置缓存正常');
      } else {
        report.push('   ! 配置缓存为空（正常情况）');
      }

    } catch (error) {
      report.push('');
      report.push(`诊断过程中发生错误: ${error}`);
      success = false;
    }

    report.push('');
    report.push(`=== 诊断结果: ${success ? '通过' : '失败'} ===`);
    
    return {
      success,
      report: report.join('\n')
    };
  }

  /**
   * 快速检查API密钥有效性
   * @returns Promise<{valid: boolean, issues: string[]}> 检查结果
   */
  static async quickCheck(): Promise<{valid: boolean, issues: string[]}> {
    const issues: string[] = [];
    let valid = true;

    try {
      const config = await ApiConfigService.getApiConfig();

      // 检查DeepSeek API Key
      if (!config.deepseekApiKey || !config.deepseekApiKey.startsWith('sk-')) {
        issues.push('DeepSeek API Key格式不正确');
        valid = false;
      }

      // 检查百度API密钥
      if (!config.baiduApiKey || config.baiduApiKey.length < 10) {
        issues.push('百度API Key格式不正确');
        valid = false;
      }

      if (!config.baiduSecretKey || config.baiduSecretKey.length < 10) {
        issues.push('百度Secret Key格式不正确');
        valid = false;
      }

    } catch (error) {
      issues.push(`配置检查失败: ${error}`);
      valid = false;
    }

    return { valid, issues };
  }

  /**
   * 重置所有缓存
   */
  static resetAllCaches(): void {
    console.log('重置所有API缓存...');
    
    // 清除配置缓存
    ApiConfigService.clearCache();
    
    // 清除百度令牌缓存
    SpeechRecognitionService.clearBaiduTokenCache();
    
    console.log('所有缓存已清除');
  }

  /**
   * 测试百度API连接
   * @returns Promise<{success: boolean, message: string}> 测试结果
   */
  static async testBaiduConnection(): Promise<{success: boolean, message: string}> {
    try {
      console.log('开始测试百度API连接...');
      
      // 清除缓存，确保使用最新配置
      SpeechRecognitionService.clearBaiduTokenCache();
      
      // 尝试获取访问令牌
      const token = await SpeechRecognitionService.getBaiduAccessToken();
      
      if (token && token.length > 0) {
        return {
          success: true,
          message: `百度API连接成功，访问令牌长度: ${token.length}`
        };
      } else {
        return {
          success: false,
          message: '百度API连接失败: 访问令牌为空'
        };
      }
    } catch (error) {
      return {
        success: false,
        message: `百度API连接失败: ${error}`
      };
    }
  }
}
