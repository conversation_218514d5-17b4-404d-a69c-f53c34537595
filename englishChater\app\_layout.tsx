import { Stack } from "expo-router";
import { useEffect } from "react";
import { ApiConfigService } from "@/services/apiConfig";

export default function RootLayout() {
  // 在应用启动时预加载API配置
  useEffect(() => {
    const loadApiConfig = async () => {
      try {
        console.log('应用启动，预加载API配置...');
        await ApiConfigService.getApiConfig();
        console.log('API配置预加载完成');
      } catch (error) {
        console.error('预加载API配置失败:', error);
      }
    };

    loadApiConfig();
  }, []);

  return (
    <Stack
      screenOptions={{
        headerShown: false,
      }}
    />
  );
}
