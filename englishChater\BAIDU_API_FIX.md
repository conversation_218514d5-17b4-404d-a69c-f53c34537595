# 百度语音识别API 401错误修复方案

## 🚨 问题描述

用户通过API配置界面修改百度语音识别API密钥后，再次使用语音识别功能时出现401认证错误：

```
ERROR  获取百度访问令牌错误: [AxiosError: Request failed with status code 401]
ERROR  百度API响应: {"error": "invalid_client", "error_description": "Client authentication failed"}
```

## 🔍 根本原因分析

### 主要问题
1. **缓存的访问令牌未清除**：`SpeechRecognitionService.baiduAccessToken` 和 `tokenExpireTime` 是静态变量，当API密钥更新后，旧的访问令牌仍然被缓存使用
2. **配置更新后未清除API服务缓存**：ApiConfigService更新配置后，没有通知ApiService清除其缓存的令牌
3. **缺乏配置验证和调试信息**：难以诊断API密钥是否正确加载

### 技术细节
- 百度API访问令牌有30天有效期，但使用错误密钥获取的令牌会立即失效
- 静态变量在应用生命周期内持续存在，不会因配置更新而自动清除
- 缺乏配置更新后的缓存清理机制

## ✅ 解决方案

### 1. 添加令牌缓存清除机制

**文件**: `services/api.ts`
```typescript
/**
 * 清除百度访问令牌缓存
 * 当API密钥更新时需要调用此方法
 */
static clearBaiduTokenCache(): void {
  console.log('清除百度访问令牌缓存');
  this.baiduAccessToken = '';
  this.tokenExpireTime = 0;
}
```

### 2. 配置保存时自动清除缓存

**文件**: `services/apiConfig.ts`
```typescript
// 在saveApiConfig方法中添加
// 清除API服务的缓存令牌（重要：防止使用旧密钥的令牌）
try {
  const { SpeechRecognitionService } = await import('./api');
  SpeechRecognitionService.clearBaiduTokenCache();
  console.log('已清除百度API令牌缓存');
} catch (error) {
  console.warn('清除API令牌缓存失败:', error);
}
```

### 3. 增强调试和验证

**文件**: `services/api.ts` - getBaiduAccessToken方法
```typescript
// 添加调试信息（不显示完整密钥，只显示前几位和长度）
console.log('百度API密钥信息:');
console.log(`- API Key: ${baiduApiKey.substring(0, 8)}... (长度: ${baiduApiKey.length})`);
console.log(`- Secret Key: ${baiduSecretKey.substring(0, 8)}... (长度: ${baiduSecretKey.length})`);

// 验证密钥格式
if (!baiduApiKey || baiduApiKey.length < 10) {
  throw new Error('百度API Key格式不正确或为空');
}
if (!baiduSecretKey || baiduSecretKey.length < 10) {
  throw new Error('百度Secret Key格式不正确或为空');
}
```

### 4. 创建API诊断工具

**新文件**: `services/apiDiagnostics.ts`
- 完整的API配置诊断功能
- 测试百度API连接
- 检查配置状态和缓存
- 生成详细的诊断报告

### 5. 增强API配置界面

**文件**: `components/ApiConfigModal.tsx`
- 添加"测试连接"按钮
- 集成API诊断功能
- 提供实时的配置验证反馈

## 🔧 修复后的工作流程

### 正常流程
1. 用户在API配置界面输入新的百度API密钥
2. 点击"保存"按钮
3. **自动清除旧的访问令牌缓存** ✅
4. 下次语音识别时使用新密钥获取新的访问令牌
5. 成功进行语音识别

### 测试流程
1. 用户可以点击"测试连接"按钮
2. 运行完整的API诊断
3. 显示详细的测试报告
4. 确认配置是否正确

## 🛠️ 使用方法

### 用户操作
1. **配置API密钥**：
   - 打开设置 → API配置
   - 输入正确的百度API Key和Secret Key
   - 点击"保存"

2. **测试连接**：
   - 在API配置界面点击"测试连接"
   - 查看诊断报告确认配置正确

3. **故障排除**：
   - 如果仍有问题，点击"重置默认"
   - 重新输入API密钥

### 开发者调试
```typescript
// 手动清除缓存
import { SpeechRecognitionService } from './services/api';
SpeechRecognitionService.clearBaiduTokenCache();

// 运行诊断
import { ApiDiagnostics } from './services/apiDiagnostics';
const result = await ApiDiagnostics.runFullDiagnostics();
console.log(result.report);
```

## 📋 验证清单

- ✅ 添加了令牌缓存清除机制
- ✅ 配置保存时自动清除缓存
- ✅ 增强了调试信息和验证
- ✅ 创建了API诊断工具
- ✅ 增强了用户界面测试功能
- ✅ 提供了详细的错误信息

## 🔍 预防措施

### 代码层面
1. **自动缓存清理**：配置更新时自动清除相关缓存
2. **详细日志**：提供足够的调试信息
3. **格式验证**：在保存前验证API密钥格式
4. **错误处理**：提供清晰的错误信息和解决建议

### 用户层面
1. **测试功能**：提供一键测试API连接
2. **诊断报告**：详细的配置状态报告
3. **帮助信息**：清晰的API密钥获取指导

## 🎯 预期效果

修复后，用户应该能够：
1. ✅ 成功更新API密钥配置
2. ✅ 立即使用新配置进行语音识别
3. ✅ 通过测试功能验证配置正确性
4. ✅ 获得清晰的错误信息和解决建议

**不再出现401认证错误！** 🎉
