import AsyncStorage from '@react-native-async-storage/async-storage';

/**
 * API配置接口
 */
export interface ApiConfig {
  deepseekApiKey: string;
  baiduApiKey: string;
  baiduSecretKey: string;
}

/**
 * 默认API配置（硬编码的备用值）
 */
const DEFAULT_API_CONFIG: ApiConfig = {
  deepseekApiKey: '***********************************',
  baiduApiKey: '4SJ6Uw96rKEEyeiuXLgrpaBi',
  baiduSecretKey: 'WzrJdgyeL9iVBgFIgFqysuFcntzyQA5g'
};

/**
 * API配置管理服务
 */
export class ApiConfigService {
  private static readonly STORAGE_KEY = 'api_config';
  private static cachedConfig: ApiConfig | null = null;

  /**
   * 获取API配置
   * 优先从用户配置读取，如果没有则使用默认值
   */
  static async getApiConfig(): Promise<ApiConfig> {
    try {
      // 如果有缓存，直接返回
      if (this.cachedConfig) {
        return this.cachedConfig;
      }

      // 从AsyncStorage读取用户配置
      const storedConfig = await AsyncStorage.getItem(this.STORAGE_KEY);
      
      if (storedConfig) {
        const userConfig = JSON.parse(storedConfig) as Partial<ApiConfig>;
        
        // 合并用户配置和默认配置（用户配置优先）
        this.cachedConfig = {
          deepseekApiKey: userConfig.deepseekApiKey || DEFAULT_API_CONFIG.deepseekApiKey,
          baiduApiKey: userConfig.baiduApiKey || DEFAULT_API_CONFIG.baiduApiKey,
          baiduSecretKey: userConfig.baiduSecretKey || DEFAULT_API_CONFIG.baiduSecretKey
        };
        
        console.log('已加载用户API配置');
      } else {
        // 使用默认配置
        this.cachedConfig = { ...DEFAULT_API_CONFIG };
        console.log('使用默认API配置');
      }

      return this.cachedConfig;
    } catch (error) {
      console.error('获取API配置失败，使用默认配置:', error);
      this.cachedConfig = { ...DEFAULT_API_CONFIG };
      return this.cachedConfig;
    }
  }

  /**
   * 保存API配置
   */
  static async saveApiConfig(config: ApiConfig): Promise<void> {
    try {
      // 验证配置
      this.validateApiConfig(config);

      // 保存到AsyncStorage
      await AsyncStorage.setItem(this.STORAGE_KEY, JSON.stringify(config));
      
      // 更新缓存
      this.cachedConfig = { ...config };
      
      console.log('API配置已保存');
    } catch (error) {
      console.error('保存API配置失败:', error);
      throw new Error('保存API配置失败，请重试');
    }
  }

  /**
   * 获取当前缓存的配置（不从存储读取）
   */
  static getCachedConfig(): ApiConfig | null {
    return this.cachedConfig;
  }

  /**
   * 清除缓存，强制下次从存储重新读取
   */
  static clearCache(): void {
    this.cachedConfig = null;
  }

  /**
   * 重置为默认配置
   */
  static async resetToDefault(): Promise<void> {
    try {
      await AsyncStorage.removeItem(this.STORAGE_KEY);
      this.cachedConfig = { ...DEFAULT_API_CONFIG };
      console.log('API配置已重置为默认值');
    } catch (error) {
      console.error('重置API配置失败:', error);
      throw new Error('重置API配置失败，请重试');
    }
  }

  /**
   * 检查是否有用户自定义配置
   */
  static async hasUserConfig(): Promise<boolean> {
    try {
      const storedConfig = await AsyncStorage.getItem(this.STORAGE_KEY);
      return storedConfig !== null;
    } catch (error) {
      console.error('检查用户配置失败:', error);
      return false;
    }
  }

  /**
   * 验证API配置
   */
  private static validateApiConfig(config: ApiConfig): void {
    if (!config.deepseekApiKey || config.deepseekApiKey.trim() === '') {
      throw new Error('DeepSeek API Key不能为空');
    }

    if (!config.baiduApiKey || config.baiduApiKey.trim() === '') {
      throw new Error('百度语音识别 API Key不能为空');
    }

    if (!config.baiduSecretKey || config.baiduSecretKey.trim() === '') {
      throw new Error('百度语音识别 Secret Key不能为空');
    }

    // 简单的格式验证
    if (!config.deepseekApiKey.startsWith('sk-')) {
      throw new Error('DeepSeek API Key格式不正确，应以"sk-"开头');
    }

    if (config.baiduApiKey.length < 10) {
      throw new Error('百度 API Key格式不正确，长度过短');
    }

    if (config.baiduSecretKey.length < 10) {
      throw new Error('百度 Secret Key格式不正确，长度过短');
    }
  }

  /**
   * 获取配置状态信息
   */
  static async getConfigStatus(): Promise<{
    hasUserConfig: boolean;
    isUsingDefault: boolean;
    configSource: 'user' | 'default';
  }> {
    const hasUser = await this.hasUserConfig();
    const config = await this.getApiConfig();
    
    return {
      hasUserConfig: hasUser,
      isUsingDefault: !hasUser,
      configSource: hasUser ? 'user' : 'default'
    };
  }

  /**
   * 导出配置（用于备份）
   */
  static async exportConfig(): Promise<string> {
    const config = await this.getApiConfig();
    return JSON.stringify(config, null, 2);
  }

  /**
   * 导入配置（从备份恢复）
   */
  static async importConfig(configJson: string): Promise<void> {
    try {
      const config = JSON.parse(configJson) as ApiConfig;
      await this.saveApiConfig(config);
    } catch (error) {
      console.error('导入配置失败:', error);
      throw new Error('配置格式不正确，导入失败');
    }
  }
}
