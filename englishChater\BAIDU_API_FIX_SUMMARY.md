# 百度语音识别API修复总结

## 🎯 修复概述

根据百度语音识别API官方文档，对项目中的语音识别模块进行了全面修复，现在完全符合百度官方示例要求。

## ✅ 测试结果

**修复前**: 错误码3300 "语音数据格式错误"
**修复后**: 成功返回 `err_no: 0, err_msg: "success."`

## 🔧 主要修复内容

### 1. 音频录制格式修复 (`services/audio.ts`)

**修复前**:
```typescript
// 使用THREE_GPP/AMR格式
outputFormat: Audio.AndroidOutputFormat.THREE_GPP,
audioEncoder: Audio.AndroidAudioEncoder.AMR_NB,
sampleRate: 8000,
```

**修复后**:
```typescript
// 严格按照百度API要求使用PCM格式
outputFormat: Audio.AndroidOutputFormat.DEFAULT, // 产生真正的WAV/PCM格式
audioEncoder: Audio.AndroidAudioEncoder.DEFAULT, // 使用DEFAULT编码器
sampleRate: 16000,  // 百度API推荐16000Hz
numberOfChannels: 1, // 单声道（百度API要求）
bitRate: 256000,    // 16bit * 16000Hz * 1channel = 256000 bps
```

### 2. API请求参数修复 (`services/api.ts`)

**关键修复点**:

#### 2.1 音频格式参数
```typescript
// 修复前：复杂的格式判断逻辑
if (fileExt === '3gp' || fileExt === 'amr') {
  audioFormat = 'amr';
} else {
  audioFormat = 'pcm';
}

// 修复后：严格按照百度官方示例
const audioFormat = 'pcm';  // 百度推荐格式
const sampleRate = 16000;   // 百度推荐采样率
```

#### 2.2 len参数计算
```typescript
// 修复前：复杂的长度计算和修正逻辑
if (wavAnalysis.pcmDataLength && wavAnalysis.pcmDataLength > 0) {
  finalDataLength = wavAnalysis.pcmDataLength;
} else if (fileSizeRatio > 0.8 && fileSizeRatio < 1.2) {
  finalDataLength = decodedDataLength;
} else {
  finalDataLength = theoreticalPcmLength;
}

// 修复后：直接使用原始文件字节数
const finalDataLength = decodedDataLength; // 使用原始文件字节数
```

#### 2.3 请求参数结构
```typescript
// 严格按照百度API官方示例构建请求参数
const requestData = {
  format: 'pcm',              // string 必填：推荐pcm文件
  rate: 16000,                // int 必填：采样率，16000、8000，固定值
  channel: 1,                 // int 必填：声道数，仅支持单声道，固定值 1
  cuid: deviceCuid,           // string 必填：用户唯一标识，长度为60字符以内
  token: accessToken,         // string 必填：access_token鉴权信息
  speech: base64Audio,        // string 必填：base64编码的音频数据
  len: finalDataLength,       // int 必填：本地语音文件的字节数，单位字节
  dev_pid: 1537               // int 选填：识别模型，1537为普通话，1737为英语
};
```

#### 2.4 请求头修复
```typescript
// 修复前：
headers: {
  'Content-Type': 'application/json',
  'Accept': 'application/json'
}

// 修复后：严格按照百度官方示例
headers: {
  'Content-Type': 'application/json'    // 百度官方要求的固定头部
}
```

#### 2.5 请求URL修复
```typescript
// 修复前：
url: 'https://vop.baidu.com/server_api'

// 修复后：严格按照百度官方示例
url: 'http://vop.baidu.com/server_api'  // 百度官方示例URL
```

## 📋 百度API官方要求对照

| 参数 | 类型 | 必填 | 官方要求 | 项目实现 |
|------|------|------|----------|----------|
| format | string | ✅ | pcm/wav/amr/m4a，推荐pcm | ✅ pcm |
| rate | int | ✅ | 16000、8000，固定值 | ✅ 16000 |
| channel | int | ✅ | 仅支持单声道，固定值 1 | ✅ 1 |
| cuid | string | ✅ | 用户唯一标识，长度60字符以内 | ✅ 设备ID |
| token | string | ✅ | access_token鉴权信息 | ✅ 动态获取 |
| speech | string | ✅ | base64编码的音频数据 | ✅ 正确编码 |
| len | int | ✅ | 原始音频文件字节数 | ✅ 原始字节数 |
| dev_pid | int | ❌ | 识别模型，1537普通话，1737英语 | ✅ 1537 |

## 🧪 测试验证

创建了专门的测试脚本 `scripts/test-baidu-api-fixed.js`：

```bash
node scripts/test-baidu-api-fixed.js
```

**测试结果**:
```json
{
  "corpus_no": "7507894754966898129",
  "err_msg": "success.",
  "err_no": 0,
  "result": ["我不知道。"],
  "sn": "384418993531748067968"
}
```

✅ **成功**: `err_no: 0` 表示API调用格式完全正确

## 🎯 关键修复要点

1. **音频格式**: 使用PCM格式，16000Hz采样率，16bit位深，单声道
2. **len参数**: 必须是原始音频文件的字节数，不是base64编码后的长度
3. **请求格式**: 严格按照百度官方JSON格式
4. **请求头**: 只需要 `Content-Type: application/json`
5. **URL**: 使用官方示例的HTTP URL

## 📝 注意事项

1. 现在录音会产生真正的WAV/PCM格式文件
2. len参数计算已简化，直接使用文件字节数
3. 所有参数都严格按照百度官方文档设置
4. 移除了复杂的格式判断和修正逻辑

## 🚀 下一步

现在语音识别模块已经修复，可以：
1. 在真实设备上测试录音和识别功能
2. 根据需要调整 `dev_pid` 参数（1537普通话，1737英语）
3. 优化错误处理和用户体验
