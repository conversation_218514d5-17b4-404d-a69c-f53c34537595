import React, { useState, useEffect } from 'react';
import {
  Modal,
  View,
  TextInput,
  TouchableOpacity,
  Alert,
  StyleSheet,
  ScrollView,
  ActivityIndicator
} from 'react-native';
import { ThemedText } from './ThemedText';
import { ThemedView } from './ThemedView';
import { IconSymbol } from './ui/IconSymbol';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import { ApiConfigService, ApiConfig } from '@/services/apiConfig';
import { ApiDiagnostics } from '@/services/apiDiagnostics';

interface ApiConfigModalProps {
  visible: boolean;
  onClose: () => void;
  onConfigSaved?: () => void;
}

export default function ApiConfigModal({ visible, onClose, onConfigSaved }: ApiConfigModalProps) {
  const colorScheme = useColorScheme() ?? 'light';
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [testing, setTesting] = useState(false);

  // 表单状态
  const [deepseekApiKey, setDeepseekApiKey] = useState('');
  const [baiduApiKey, setBaiduApiKey] = useState('');
  const [baiduSecretKey, setBaiduSecretKey] = useState('');

  // 显示/隐藏密钥
  const [showDeepseekKey, setShowDeepseekKey] = useState(false);
  const [showBaiduKey, setShowBaiduKey] = useState(false);
  const [showBaiduSecret, setShowBaiduSecret] = useState(false);

  // 加载当前配置
  useEffect(() => {
    if (visible) {
      loadCurrentConfig();
    }
  }, [visible]);

  const loadCurrentConfig = async () => {
    try {
      setLoading(true);
      const config = await ApiConfigService.getApiConfig();

      setDeepseekApiKey(config.deepseekApiKey);
      setBaiduApiKey(config.baiduApiKey);
      setBaiduSecretKey(config.baiduSecretKey);
    } catch (error) {
      console.error('加载配置失败:', error);
      Alert.alert('错误', '加载当前配置失败');
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    try {
      setSaving(true);

      // 验证输入
      if (!deepseekApiKey.trim()) {
        Alert.alert('错误', 'DeepSeek API Key不能为空');
        return;
      }

      if (!baiduApiKey.trim()) {
        Alert.alert('错误', '百度语音识别 API Key不能为空');
        return;
      }

      if (!baiduSecretKey.trim()) {
        Alert.alert('错误', '百度语音识别 Secret Key不能为空');
        return;
      }

      // 构建配置对象
      const config: ApiConfig = {
        deepseekApiKey: deepseekApiKey.trim(),
        baiduApiKey: baiduApiKey.trim(),
        baiduSecretKey: baiduSecretKey.trim()
      };

      // 保存配置
      await ApiConfigService.saveApiConfig(config);

      // 显示成功提示
      Alert.alert('成功', 'API配置已保存', [
        {
          text: '确定',
          onPress: () => {
            onConfigSaved?.();
            onClose();
          }
        }
      ]);
    } catch (error) {
      console.error('保存配置失败:', error);
      Alert.alert('错误', error instanceof Error ? error.message : '保存配置失败，请重试');
    } finally {
      setSaving(false);
    }
  };

  const handleReset = () => {
    Alert.alert(
      '重置配置',
      '确定要重置为默认配置吗？这将清除您的自定义设置。',
      [
        { text: '取消', style: 'cancel' },
        {
          text: '重置',
          style: 'destructive',
          onPress: async () => {
            try {
              await ApiConfigService.resetToDefault();
              await loadCurrentConfig();
              Alert.alert('成功', '配置已重置为默认值');
            } catch (error) {
              Alert.alert('错误', '重置配置失败，请重试');
            }
          }
        }
      ]
    );
  };

  const handleTest = async () => {
    try {
      setTesting(true);

      // 运行完整诊断
      const result = await ApiDiagnostics.runFullDiagnostics();

      // 显示诊断报告
      Alert.alert(
        result.success ? '测试通过' : '测试失败',
        result.report,
        [{ text: '确定' }],
        { cancelable: true }
      );
    } catch (error) {
      Alert.alert('测试失败', `诊断过程中发生错误: ${error}`);
    } finally {
      setTesting(false);
    }
  };

  const renderPasswordInput = (
    value: string,
    onChangeText: (text: string) => void,
    placeholder: string,
    showPassword: boolean,
    toggleShow: () => void
  ) => (
    <View style={styles.passwordInputContainer}>
      <TextInput
        style={[
          styles.input,
          styles.passwordInput,
          {
            color: Colors[colorScheme].text,
            borderColor: Colors[colorScheme].border
          }
        ]}
        value={value}
        onChangeText={onChangeText}
        placeholder={placeholder}
        placeholderTextColor={Colors[colorScheme].tabIconDefault}
        secureTextEntry={!showPassword}
        autoCapitalize="none"
        autoCorrect={false}
      />
      <TouchableOpacity
        style={styles.eyeButton}
        onPress={toggleShow}
      >
        <IconSymbol
          size={20}
          name={showPassword ? "eye.slash" : "eye"}
          color={Colors[colorScheme].tabIconDefault}
        />
      </TouchableOpacity>
    </View>
  );

  return (
    <Modal
      animationType="slide"
      transparent={true}
      visible={visible}
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <ThemedView style={styles.modalContent}>
          <View style={styles.header}>
            <ThemedText type="subtitle" style={styles.title}>
              API配置
            </ThemedText>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <IconSymbol size={24} name="xmark" color={Colors[colorScheme].text} />
            </TouchableOpacity>
          </View>

          {loading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color={Colors[colorScheme].tint} />
              <ThemedText style={styles.loadingText}>加载配置中...</ThemedText>
            </View>
          ) : (
            <ScrollView style={styles.form} showsVerticalScrollIndicator={false}>
              <View style={styles.section}>
                <ThemedText type="defaultSemiBold" style={styles.sectionTitle}>
                  DeepSeek API Key
                </ThemedText>
                <ThemedText style={styles.sectionDescription}>
                  用于AI对话功能，请在DeepSeek官网获取
                </ThemedText>
                {renderPasswordInput(
                  deepseekApiKey,
                  setDeepseekApiKey,
                  '输入DeepSeek API Key...',
                  showDeepseekKey,
                  () => setShowDeepseekKey(!showDeepseekKey)
                )}
              </View>

              <View style={styles.section}>
                <ThemedText type="defaultSemiBold" style={styles.sectionTitle}>
                  百度语音识别 API Key
                </ThemedText>
                <ThemedText style={styles.sectionDescription}>
                  用于语音识别功能，请在百度智能云获取
                </ThemedText>
                {renderPasswordInput(
                  baiduApiKey,
                  setBaiduApiKey,
                  '输入百度 API Key...',
                  showBaiduKey,
                  () => setShowBaiduKey(!showBaiduKey)
                )}
              </View>

              <View style={styles.section}>
                <ThemedText type="defaultSemiBold" style={styles.sectionTitle}>
                  百度语音识别 Secret Key
                </ThemedText>
                <ThemedText style={styles.sectionDescription}>
                  用于百度API鉴权，与API Key配套使用
                </ThemedText>
                {renderPasswordInput(
                  baiduSecretKey,
                  setBaiduSecretKey,
                  '输入百度 Secret Key...',
                  showBaiduSecret,
                  () => setShowBaiduSecret(!showBaiduSecret)
                )}
              </View>

              <View style={styles.helpSection}>
                <ThemedText style={styles.helpTitle}>💡 获取API密钥帮助</ThemedText>
                <ThemedText style={styles.helpText}>
                  • DeepSeek: 访问 platform.deepseek.com 注册并获取API密钥{'\n'}
                  • 百度语音: 访问 cloud.baidu.com 创建语音识别应用获取密钥
                </ThemedText>
              </View>
            </ScrollView>
          )}

          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={styles.resetButton}
              onPress={handleReset}
              disabled={saving || testing}
            >
              <ThemedText style={styles.resetButtonText}>重置默认</ThemedText>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.testButton,
                { borderColor: Colors[colorScheme].tint }
              ]}
              onPress={handleTest}
              disabled={saving || testing || loading}
            >
              {testing ? (
                <ActivityIndicator size="small" color={Colors[colorScheme].tint} />
              ) : (
                <ThemedText style={[styles.testButtonText, { color: Colors[colorScheme].tint }]}>
                  测试连接
                </ThemedText>
              )}
            </TouchableOpacity>

            <View style={styles.rightButtons}>
              <TouchableOpacity
                style={styles.cancelButton}
                onPress={onClose}
                disabled={saving || testing}
              >
                <ThemedText>取消</ThemedText>
              </TouchableOpacity>

              <TouchableOpacity
                style={[
                  styles.saveButton,
                  { backgroundColor: Colors[colorScheme].tint }
                ]}
                onPress={handleSave}
                disabled={saving || loading || testing}
              >
                {saving ? (
                  <ActivityIndicator size="small" color="white" />
                ) : (
                  <ThemedText style={styles.saveButtonText}>保存</ThemedText>
                )}
              </TouchableOpacity>
            </View>
          </View>
        </ThemedView>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    width: '90%',
    maxHeight: '80%',
    borderRadius: 12,
    elevation: 5,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    paddingBottom: 10,
  },
  title: {
    flex: 1,
  },
  closeButton: {
    padding: 4,
  },
  loadingContainer: {
    padding: 40,
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    opacity: 0.7,
  },
  form: {
    paddingHorizontal: 20,
    maxHeight: 400,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    marginBottom: 4,
  },
  sectionDescription: {
    fontSize: 12,
    opacity: 0.7,
    marginBottom: 8,
  },
  passwordInputContainer: {
    position: 'relative',
  },
  input: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
  },
  passwordInput: {
    paddingRight: 50,
  },
  eyeButton: {
    position: 'absolute',
    right: 12,
    top: 12,
    padding: 4,
  },
  helpSection: {
    backgroundColor: 'rgba(0, 122, 255, 0.1)',
    padding: 12,
    borderRadius: 8,
    marginTop: 8,
  },
  helpTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 4,
  },
  helpText: {
    fontSize: 12,
    opacity: 0.8,
    lineHeight: 16,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    paddingTop: 10,
    flexWrap: 'wrap',
    gap: 8,
  },
  resetButton: {
    padding: 12,
  },
  resetButtonText: {
    color: '#ff6b6b',
    fontSize: 14,
  },
  rightButtons: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  cancelButton: {
    padding: 12,
    marginRight: 12,
  },
  saveButton: {
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
    minWidth: 80,
    alignItems: 'center',
  },
  saveButtonText: {
    color: 'white',
    fontWeight: '600',
  },
  testButton: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    borderWidth: 1,
    alignItems: 'center',
    minWidth: 80,
  },
  testButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
});
