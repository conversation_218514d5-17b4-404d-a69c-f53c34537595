import { useState, useEffect, useRef, useCallback } from 'react';
import {
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Modal,
  TextInput,
  View,
  Alert,
  ActivityIndicator,
  RefreshControl,
  Animated,
  Dimensions,
  Keyboard,
  Platform,
  ScrollView
} from 'react-native';
import { useRouter } from 'expo-router';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import { ConversationService, Conversation, PaginationOptions } from '@/services/conversation';
import ApiConfigModal from '@/components/ApiConfigModal';

export default function ConversationListScreen() {
  const router = useRouter();
  const colorScheme = useColorScheme() ?? 'light';
  const [newConversationModalVisible, setNewConversationModalVisible] = useState(false);
  const [editTitleModalVisible, setEditTitleModalVisible] = useState(false);
  const [editRoleModalVisible, setEditRoleModalVisible] = useState(false);
  const [roleInput, setRoleInput] = useState('');
  const [titleInput, setTitleInput] = useState('');
  const [editingConversationId, setEditingConversationId] = useState<string | null>(null);
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [showSearch, setShowSearch] = useState(false);
  const [selectedConversation, setSelectedConversation] = useState<Conversation | null>(null);
  const [actionMenuVisible, setActionMenuVisible] = useState(false);
  const [showRoleTemplates, setShowRoleTemplates] = useState(false);
  const [apiConfigModalVisible, setApiConfigModalVisible] = useState(false);

  // 分页参数
  const [pagination, setPagination] = useState<PaginationOptions>({
    page: 0,
    limit: 20
  });
  const [hasMoreConversations, setHasMoreConversations] = useState(true);

  // 创建对话服务实例
  const conversationServiceRef = useRef(new ConversationService());
  const conversationService = conversationServiceRef.current;

  // 搜索栏动画
  const searchBarHeight = useRef(new Animated.Value(0)).current;
  const searchBarOpacity = useRef(new Animated.Value(0)).current;

  // 加载对话列表
  const loadConversations = useCallback(async (refresh = false) => {
    try {
      if (refresh) {
        setIsRefreshing(true);
        setPagination({ page: 0, limit: 20 });
      } else if (!refresh && !hasMoreConversations) {
        return;
      } else {
        setIsLoading(true);
      }

      const options = {
        ...pagination,
        page: refresh ? 0 : pagination.page,
        search: searchQuery.trim() || undefined
      };

      const convs = await conversationService.getConversationsPaginated(options);

      if (refresh || pagination.page === 0) {
        setConversations(convs);
      } else {
        setConversations(prev => [...prev, ...convs]);
      }

      // 检查是否还有更多对话
      setHasMoreConversations(convs.length === pagination.limit);

      if (!refresh) {
        setPagination(prev => ({ ...prev, page: prev.page + 1 }));
      }
    } catch (error) {
      console.error('加载对话列表失败:', error);
      Alert.alert('错误', '加载对话列表失败，请重试');
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  }, [pagination, hasMoreConversations, searchQuery, conversationService]);

  // 初始加载
  useEffect(() => {
    loadConversations(true);
  }, [searchQuery]);

  // 处理下拉刷新
  const handleRefresh = useCallback(() => {
    loadConversations(true);
  }, [loadConversations]);

  // 处理加载更多
  const handleLoadMore = useCallback(() => {
    if (!isLoading && hasMoreConversations) {
      loadConversations();
    }
  }, [isLoading, hasMoreConversations, loadConversations]);

  // 创建新对话
  const createNewConversation = async () => {
    if (roleInput.trim() === '') return;

    try {
      // 创建新对话
      const newConversation = await conversationService.createConversation(roleInput);

      // 添加欢迎消息
      await conversationService.addMessage(
        newConversation.id,
        "Hello! I'm ready to help you practice English. What would you like to talk about today?",
        false
      );

      // 更新状态
      setConversations([newConversation, ...conversations]);
      setRoleInput('');
      setNewConversationModalVisible(false);

      // 导航到对话页面
      router.push({
        pathname: '/chat',
        params: {
          conversationId: newConversation.id,
          role: newConversation.role
        }
      });
    } catch (error) {
      console.error('创建对话失败:', error);
      Alert.alert('错误', '创建对话失败，请重试');
    }
  };

  // 打开已有对话
  const openConversation = (conversation: Conversation) => {
    router.push({
      pathname: '/chat',
      params: {
        conversationId: conversation.id,
        role: conversation.role
      }
    });
  };

  // 删除对话
  const deleteConversation = async (id: string) => {
    try {
      // 显示加载状态
      setIsLoading(true);

      // 删除对话
      await conversationService.deleteConversation(id);

      // 更新本地状态
      setConversations(prev => prev.filter(conv => conv.id !== id));
      setActionMenuVisible(false);
      setSelectedConversation(null);

      // 显示成功提示
      Alert.alert('成功', '对话已删除');
    } catch (error) {
      console.error('删除对话失败:', error);
      Alert.alert('错误', '删除对话失败，请重试');
    } finally {
      setIsLoading(false);
    }
  };

  // 确认删除对话
  const confirmDeleteConversation = (conversation: Conversation) => {
    // 关闭操作菜单
    setActionMenuVisible(false);

    // 显示确认对话框
    setTimeout(() => {
      Alert.alert(
        '删除对话',
        `确定要删除"${conversation.title}"吗？\n\n此操作不可撤销，对话中的所有消息将被永久删除。`,
        [
          {
            text: '取消',
            style: 'cancel',
            onPress: () => console.log('取消删除对话')
          },
          {
            text: '删除',
            style: 'destructive',
            onPress: () => deleteConversation(conversation.id)
          }
        ],
        { cancelable: true }
      );
    }, 300); // 延迟显示，确保操作菜单已关闭
  };

  // 更新对话标题
  const updateConversationTitle = async () => {
    if (!editingConversationId || titleInput.trim() === '') return;

    try {
      await conversationService.updateConversationTitle(editingConversationId, titleInput);

      // 更新本地状态
      setConversations(prev =>
        prev.map(conv =>
          conv.id === editingConversationId
            ? { ...conv, title: titleInput }
            : conv
        )
      );

      // 重置状态
      setEditTitleModalVisible(false);
      setTitleInput('');
      setEditingConversationId(null);
    } catch (error) {
      console.error('更新对话标题失败:', error);
      Alert.alert('错误', '更新对话标题失败，请重试');
    }
  };

  // 打开编辑标题模态框
  const openEditTitleModal = (conversation: Conversation) => {
    setEditingConversationId(conversation.id);
    setTitleInput(conversation.title);
    setEditTitleModalVisible(true);
    setActionMenuVisible(false);
  };

  // 打开编辑模型身份模态框
  const openEditRoleModal = (conversation: Conversation) => {
    setEditingConversationId(conversation.id);
    setRoleInput(conversation.role);
    setEditRoleModalVisible(true);
    setShowRoleTemplates(false);
    setActionMenuVisible(false);
  };

  // 更新对话模型身份
  const updateConversationRole = async () => {
    if (!editingConversationId || roleInput.trim() === '') return;

    try {
      const updatedConversation = await conversationService.updateConversationRole(
        editingConversationId,
        roleInput
      );

      if (updatedConversation) {
        // 更新本地状态
        setConversations(prev =>
          prev.map(conv =>
            conv.id === editingConversationId
              ? updatedConversation
              : conv
          )
        );
      }

      // 重置状态
      setEditRoleModalVisible(false);
      setRoleInput('');
      setEditingConversationId(null);
      setShowRoleTemplates(false);
    } catch (error) {
      console.error('更新对话模型身份失败:', error);
      Alert.alert('错误', '更新对话模型身份失败，请重试');
    }
  };

  // 切换对话置顶状态
  const togglePinConversation = async (conversation: Conversation) => {
    try {
      const updatedConversation = await conversationService.toggleConversationPin(conversation.id);
      if (updatedConversation) {
        // 更新本地状态
        setConversations(prev => {
          const filtered = prev.filter(c => c.id !== conversation.id);
          return updatedConversation.pinnedAt
            ? [updatedConversation, ...filtered] // 置顶的放在最前面
            : [...filtered, updatedConversation]; // 取消置顶的放在最后面
        });
      }
      setActionMenuVisible(false);
    } catch (error) {
      console.error('切换置顶状态失败:', error);
      Alert.alert('错误', '操作失败，请重试');
    }
  };

  // 切换搜索栏显示
  const toggleSearchBar = () => {
    if (showSearch) {
      // 隐藏搜索栏
      Keyboard.dismiss();
      Animated.parallel([
        Animated.timing(searchBarHeight, {
          toValue: 0,
          duration: 300,
          useNativeDriver: false
        }),
        Animated.timing(searchBarOpacity, {
          toValue: 0,
          duration: 300,
          useNativeDriver: false
        })
      ]).start(() => {
        setShowSearch(false);
        setSearchQuery('');
      });
    } else {
      // 显示搜索栏
      setShowSearch(true);
      Animated.parallel([
        Animated.timing(searchBarHeight, {
          toValue: 50,
          duration: 300,
          useNativeDriver: false
        }),
        Animated.timing(searchBarOpacity, {
          toValue: 1,
          duration: 300,
          useNativeDriver: false
        })
      ]).start();
    }
  };

  // 渲染对话项
  const renderConversationItem = ({ item }: { item: Conversation }) => {
    // 获取最后更新时间
    const now = new Date();
    const updatedAt = new Date(item.updatedAt);
    const isToday = updatedAt.getDate() === now.getDate() &&
                    updatedAt.getMonth() === now.getMonth() &&
                    updatedAt.getFullYear() === now.getFullYear();

    // 格式化时间
    const timestamp = isToday
      ? updatedAt.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
      : updatedAt.toLocaleDateString();

    // 长按处理
    const handleLongPress = () => {
      setSelectedConversation(item);
      setActionMenuVisible(true);
    };

    return (
      <TouchableOpacity
        style={[
          styles.conversationItem,
          item.pinnedAt && styles.pinnedConversation
        ]}
        onPress={() => openConversation(item)}
        onLongPress={handleLongPress}
        delayLongPress={500}
      >
        {item.pinnedAt && (
          <View style={styles.pinnedIndicator}>
            <IconSymbol size={16} name="chevron.up" color={Colors[colorScheme].tint} />
          </View>
        )}

        <View style={styles.conversationContent}>
          <ThemedText type="defaultSemiBold" numberOfLines={1}>
            {item.title}
          </ThemedText>

          {item.summary ? (
            <ThemedText style={styles.summaryText} numberOfLines={1}>
              {item.summary}
            </ThemedText>
          ) : null}

          <ThemedText numberOfLines={1} style={styles.lastMessage}>
            {item.lastMessagePreview || '开始新对话'}
          </ThemedText>
        </View>

        <View style={styles.conversationMeta}>
          <ThemedText style={styles.timestamp}>{timestamp}</ThemedText>
          <ThemedText style={styles.messageCount}>
            {item.totalMessages || 0}条消息
          </ThemedText>
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <ThemedView style={styles.container}>
      <ThemedView style={styles.header}>
        <ThemedText type="title">英语对话练习</ThemedText>

        <View style={styles.headerButtons}>
          <TouchableOpacity
            style={styles.headerButton}
            onPress={() => setApiConfigModalVisible(true)}
          >
            <IconSymbol
              size={24}
              name="gear"
              color={Colors[colorScheme].text}
            />
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.headerButton}
            onPress={toggleSearchBar}
          >
            <IconSymbol
              size={24}
              name={showSearch ? "xmark" : "magnifyingglass"}
              color={Colors[colorScheme].text}
            />
          </TouchableOpacity>
        </View>
      </ThemedView>

      {/* 搜索栏 */}
      {showSearch && (
        <Animated.View
          style={[
            styles.searchContainer,
            {
              height: searchBarHeight,
              opacity: searchBarOpacity
            }
          ]}
        >
          <TextInput
            style={[
              styles.searchInput,
              {
                color: Colors[colorScheme].text,
                borderColor: Colors[colorScheme].border
              }
            ]}
            value={searchQuery}
            onChangeText={setSearchQuery}
            placeholder="搜索对话..."
            placeholderTextColor={Colors[colorScheme].tabIconDefault}
          />
        </Animated.View>
      )}

      {isLoading && conversations.length === 0 ? (
        <ThemedView style={styles.emptyState}>
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={Colors[colorScheme].tint} />
            <ThemedText style={styles.loadingText}>加载对话列表...</ThemedText>
          </View>
        </ThemedView>
      ) : conversations.length === 0 ? (
        <ThemedView style={styles.emptyState}>
          <ThemedText style={styles.emptyStateText}>
            {searchQuery ? '没有找到匹配的对话' : '还没有对话，点击下方按钮开始新对话'}
          </ThemedText>
        </ThemedView>
      ) : (
        <FlatList
          data={conversations}
          renderItem={renderConversationItem}
          keyExtractor={item => item.id}
          style={styles.list}
          refreshControl={
            <RefreshControl
              refreshing={isRefreshing}
              onRefresh={handleRefresh}
              colors={[Colors[colorScheme].tint]}
              tintColor={Colors[colorScheme].tint}
            />
          }
          onEndReached={handleLoadMore}
          onEndReachedThreshold={0.5}
          ListFooterComponent={
            isLoading && conversations.length > 0 ? (
              <View style={styles.loadingMore}>
                <ActivityIndicator color={Colors[colorScheme].tint} />
                <ThemedText style={styles.loadingMoreText}>加载更多...</ThemedText>
              </View>
            ) : null
          }
        />
      )}

      <TouchableOpacity
        style={[
          styles.newButton,
          { backgroundColor: Colors[colorScheme].tint }
        ]}
        onPress={() => setNewConversationModalVisible(true)}
      >
        <ThemedText style={styles.newButtonText}>新建对话</ThemedText>
      </TouchableOpacity>

      {/* 新建对话弹窗 */}
      <Modal
        animationType="slide"
        transparent={true}
        visible={newConversationModalVisible}
        onRequestClose={() => setNewConversationModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <ThemedView style={styles.modalContent}>
            <ThemedText type="subtitle" style={styles.modalTitle}>
              设置模型身份
            </ThemedText>

            <ThemedText style={styles.modalDescription}>
              请描述您希望模型扮演的角色，例如：
              "You are an English speaker and a mountaineering expert."
            </ThemedText>

            {/* 预设模板切换按钮 */}
            <TouchableOpacity
              style={styles.templateToggleButton}
              onPress={() => setShowRoleTemplates(!showRoleTemplates)}
            >
              <ThemedText style={styles.templateToggleText}>
                {showRoleTemplates ? '自定义输入' : '选择预设模板'}
              </ThemedText>
              <IconSymbol
                size={16}
                name={showRoleTemplates ? "pencil" : "list.bullet"}
                color={Colors[colorScheme].tint}
              />
            </TouchableOpacity>

            {showRoleTemplates ? (
              // 预设模板列表（使用ScrollView使其可滚动）
              <ScrollView
                style={styles.templateList}
                contentContainerStyle={styles.templateListContent}
                showsVerticalScrollIndicator={true}
                persistentScrollbar={true}
              >
                {conversationService.roleTemplates.map(template => (
                  <TouchableOpacity
                    key={template.id}
                    style={[
                      styles.templateItem,
                      { borderColor: Colors[colorScheme].border }
                    ]}
                    onPress={() => {
                      setRoleInput(template.description);
                      setShowRoleTemplates(false);
                    }}
                  >
                    <ThemedText type="defaultSemiBold">{template.name}</ThemedText>
                    <ThemedText style={styles.templateDescription} numberOfLines={2}>
                      {template.description}
                    </ThemedText>
                  </TouchableOpacity>
                ))}
              </ScrollView>
            ) : (
              // 自定义输入
              <TextInput
                style={[
                  styles.roleInput,
                  {
                    color: Colors[colorScheme].text,
                    borderColor: Colors[colorScheme].border
                  }
                ]}
                value={roleInput}
                onChangeText={setRoleInput}
                placeholder="输入模型身份描述..."
                placeholderTextColor={Colors[colorScheme].tabIconDefault}
                multiline
              />
            )}

            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={styles.cancelButton}
                onPress={() => {
                  setNewConversationModalVisible(false);
                  setShowRoleTemplates(false);
                  setRoleInput('');
                }}
              >
                <ThemedText>取消</ThemedText>
              </TouchableOpacity>

              <TouchableOpacity
                style={[
                  styles.confirmButton,
                  { backgroundColor: Colors[colorScheme].tint }
                ]}
                onPress={createNewConversation}
              >
                <ThemedText style={styles.confirmButtonText}>确认</ThemedText>
              </TouchableOpacity>
            </View>
          </ThemedView>
        </View>
      </Modal>

      {/* 编辑标题弹窗 */}
      <Modal
        animationType="slide"
        transparent={true}
        visible={editTitleModalVisible}
        onRequestClose={() => setEditTitleModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <ThemedView style={styles.modalContent}>
            <ThemedText type="subtitle" style={styles.modalTitle}>
              编辑对话标题
            </ThemedText>

            <TextInput
              style={[
                styles.titleInput,
                {
                  color: Colors[colorScheme].text,
                  borderColor: Colors[colorScheme].border
                }
              ]}
              value={titleInput}
              onChangeText={setTitleInput}
              placeholder="输入新标题..."
              placeholderTextColor={Colors[colorScheme].tabIconDefault}
            />

            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={styles.cancelButton}
                onPress={() => setEditTitleModalVisible(false)}
              >
                <ThemedText>取消</ThemedText>
              </TouchableOpacity>

              <TouchableOpacity
                style={[
                  styles.confirmButton,
                  { backgroundColor: Colors[colorScheme].tint }
                ]}
                onPress={updateConversationTitle}
              >
                <ThemedText style={styles.confirmButtonText}>确认</ThemedText>
              </TouchableOpacity>
            </View>
          </ThemedView>
        </View>
      </Modal>

      {/* 编辑模型身份弹窗 */}
      <Modal
        animationType="slide"
        transparent={true}
        visible={editRoleModalVisible}
        onRequestClose={() => setEditRoleModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <ThemedView style={styles.modalContent}>
            <ThemedText type="subtitle" style={styles.modalTitle}>
              修改模型身份
            </ThemedText>

            <ThemedText style={styles.modalDescription}>
              修改模型身份将在下一条消息开始生效。
              请确保包含基础语句："You are an English speaker"
            </ThemedText>

            {/* 预设模板切换按钮 */}
            <TouchableOpacity
              style={styles.templateToggleButton}
              onPress={() => setShowRoleTemplates(!showRoleTemplates)}
            >
              <ThemedText style={styles.templateToggleText}>
                {showRoleTemplates ? '自定义输入' : '选择预设模板'}
              </ThemedText>
              <IconSymbol
                size={16}
                name={showRoleTemplates ? "pencil" : "list.bullet"}
                color={Colors[colorScheme].tint}
              />
            </TouchableOpacity>

            {showRoleTemplates ? (
              // 预设模板列表（使用ScrollView使其可滚动）
              <ScrollView
                style={styles.templateList}
                contentContainerStyle={styles.templateListContent}
                showsVerticalScrollIndicator={true}
                persistentScrollbar={true}
              >
                {conversationService.roleTemplates.map(template => (
                  <TouchableOpacity
                    key={template.id}
                    style={[
                      styles.templateItem,
                      { borderColor: Colors[colorScheme].border }
                    ]}
                    onPress={() => {
                      setRoleInput(template.description);
                      setShowRoleTemplates(false);
                    }}
                  >
                    <ThemedText type="defaultSemiBold">{template.name}</ThemedText>
                    <ThemedText style={styles.templateDescription} numberOfLines={2}>
                      {template.description}
                    </ThemedText>
                  </TouchableOpacity>
                ))}
              </ScrollView>
            ) : (
              // 自定义输入
              <TextInput
                style={[
                  styles.roleInput,
                  {
                    color: Colors[colorScheme].text,
                    borderColor: Colors[colorScheme].border
                  }
                ]}
                value={roleInput}
                onChangeText={setRoleInput}
                placeholder="输入模型身份描述..."
                placeholderTextColor={Colors[colorScheme].tabIconDefault}
                multiline
              />
            )}

            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={styles.cancelButton}
                onPress={() => {
                  setEditRoleModalVisible(false);
                  setShowRoleTemplates(false);
                }}
              >
                <ThemedText>取消</ThemedText>
              </TouchableOpacity>

              <TouchableOpacity
                style={[
                  styles.confirmButton,
                  { backgroundColor: Colors[colorScheme].tint }
                ]}
                onPress={updateConversationRole}
              >
                <ThemedText style={styles.confirmButtonText}>确认</ThemedText>
              </TouchableOpacity>
            </View>
          </ThemedView>
        </View>
      </Modal>

      {/* 操作菜单 */}
      <Modal
        animationType="fade"
        transparent={true}
        visible={actionMenuVisible}
        onRequestClose={() => setActionMenuVisible(false)}
      >
        <TouchableOpacity
          style={styles.actionMenuOverlay}
          activeOpacity={1}
          onPress={() => setActionMenuVisible(false)}
        >
          {selectedConversation && (
            <ThemedView style={styles.actionMenu}>
              <TouchableOpacity
                style={styles.actionMenuItem}
                onPress={() => openEditTitleModal(selectedConversation)}
              >
                <IconSymbol size={20} name="pencil" color={Colors[colorScheme].text} />
                <ThemedText style={styles.actionMenuItemText}>编辑标题</ThemedText>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.actionMenuItem}
                onPress={() => openEditRoleModal(selectedConversation)}
              >
                <IconSymbol size={20} name="person.fill" color={Colors[colorScheme].text} />
                <ThemedText style={styles.actionMenuItemText}>修改模型身份</ThemedText>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.actionMenuItem}
                onPress={() => togglePinConversation(selectedConversation)}
              >
                <IconSymbol
                  size={20}
                  name={selectedConversation.pinnedAt ? "pin.slash" : "pin"}
                  color={Colors[colorScheme].text}
                />
                <ThemedText style={styles.actionMenuItemText}>
                  {selectedConversation.pinnedAt ? '取消置顶' : '置顶对话'}
                </ThemedText>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.actionMenuItem, styles.deleteMenuItem]}
                onPress={() => confirmDeleteConversation(selectedConversation)}
              >
                <IconSymbol size={20} name="trash" color="red" />
                <ThemedText style={styles.deleteMenuItemText}>删除对话</ThemedText>
              </TouchableOpacity>
            </ThemedView>
          )}
        </TouchableOpacity>
      </Modal>

      {/* API配置模态框 */}
      <ApiConfigModal
        visible={apiConfigModalVisible}
        onClose={() => setApiConfigModalVisible(false)}
        onConfigSaved={() => {
          // 配置保存后的回调，可以在这里刷新相关状态
          console.log('API配置已更新');
        }}
      />
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  header: {
    marginTop: 40,
    marginBottom: 20,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerButtons: {
    flexDirection: 'row',
  },
  headerButton: {
    padding: 8,
    borderRadius: 20,
  },
  searchContainer: {
    marginBottom: 16,
    overflow: 'hidden',
  },
  searchInput: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
  },
  list: {
    flex: 1,
  },
  conversationItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  pinnedConversation: {
    backgroundColor: 'rgba(0, 122, 255, 0.05)',
  },
  pinnedIndicator: {
    position: 'absolute',
    top: 8,
    right: 8,
  },
  conversationContent: {
    flex: 1,
    marginRight: 12,
  },
  summaryText: {
    fontSize: 13,
    marginTop: 2,
    opacity: 0.8,
  },
  lastMessage: {
    marginTop: 4,
    fontSize: 14,
    opacity: 0.6,
  },
  conversationMeta: {
    alignItems: 'flex-end',
    justifyContent: 'space-between',
  },
  timestamp: {
    fontSize: 12,
    opacity: 0.5,
    marginBottom: 4,
  },
  messageCount: {
    fontSize: 11,
    opacity: 0.5,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyStateText: {
    textAlign: 'center',
    opacity: 0.7,
  },
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingText: {
    marginTop: 16,
    opacity: 0.7,
  },
  loadingMore: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  loadingMoreText: {
    marginLeft: 8,
    opacity: 0.7,
  },
  newButton: {
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 16,
  },
  newButtonText: {
    color: 'white',
    fontWeight: 'bold',
  },
  modalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    width: '80%',
    padding: 20,
    borderRadius: 8,
    elevation: 5,
  },
  modalTitle: {
    marginBottom: 16,
  },
  modalDescription: {
    marginBottom: 16,
    opacity: 0.7,
  },
  roleInput: {
    borderWidth: 1,
    borderRadius: 4,
    padding: 12,
    minHeight: 100,
    textAlignVertical: 'top',
  },
  titleInput: {
    borderWidth: 1,
    borderRadius: 4,
    padding: 12,
    fontSize: 16,
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: 20,
  },
  cancelButton: {
    padding: 10,
    marginRight: 10,
  },
  confirmButton: {
    padding: 10,
    borderRadius: 4,
    minWidth: 80,
    alignItems: 'center',
  },
  confirmButtonText: {
    color: 'white',
  },
  actionMenuOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  actionMenu: {
    width: '70%',
    borderRadius: 8,
    overflow: 'hidden',
  },
  actionMenuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
  },
  actionMenuItemText: {
    marginLeft: 12,
    fontSize: 16,
  },
  deleteMenuItem: {
    borderBottomWidth: 0,
  },
  deleteMenuItemText: {
    marginLeft: 12,
    fontSize: 16,
    color: 'red',
  },
  templateToggleButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
    padding: 8,
    marginBottom: 8,
  },
  templateToggleText: {
    marginRight: 8,
    fontSize: 14,
  },
  templateList: {
    marginBottom: 16,
    maxHeight: 200,
  },
  templateListContent: {
    paddingRight: 8, // 为滚动条留出空间
  },
  templateItem: {
    padding: 12,
    borderWidth: 1,
    borderRadius: 8,
    marginBottom: 8,
  },
  templateDescription: {
    fontSize: 12,
    marginTop: 4,
    opacity: 0.7,
  },
});
