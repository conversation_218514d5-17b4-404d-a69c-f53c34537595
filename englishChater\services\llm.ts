import axios from 'axios';
import { ApiConfigService } from './apiConfig';

// API端点
const DEEPSEEK_API_URL = 'https://api.deepseek.com/v1/chat/completions';

// 消息类型定义
export interface Message {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

// LLM提供商枚举
export enum LLMProvider {
  DeepSeek = 'deepseek'
}

// LLM模型配置
interface LLMConfig {
  provider: LLMProvider;
  model: string;
  temperature: number;
  maxTokens: number;
}

// 默认配置
const DEFAULT_CONFIG: LLMConfig = {
  provider: LLMProvider.DeepSeek, // 默认使用DeepSeek
  model: 'deepseek-chat',
  temperature: 0.7,
  maxTokens: 1000
};

/**
 * 大语言模型服务类
 */
export class LLMService {
  private config: LLMConfig;

  /**
   * 构造函数
   * @param config LLM配置（可选）
   */
  constructor(config?: Partial<LLMConfig>) {
    this.config = { ...DEFAULT_CONFIG, ...config };
  }

  /**
   * 发送消息到LLM API并获取回复
   * @param messages 消息历史
   * @returns Promise<string> AI回复文本
   */
  async sendMessage(messages: Message[]): Promise<string> {
    try {
      // 获取API配置
      const config = await ApiConfigService.getApiConfig();

      // 目前只支持DeepSeek
      const apiUrl = DEEPSEEK_API_URL;
      const apiKey = config.deepseekApiKey;

      // 检查API密钥是否有效
      if (!apiKey || !apiKey.startsWith('sk-')) {
        throw new Error('请在设置中配置有效的DeepSeek API密钥');
      }

      // 构建请求体
      const requestBody = {
        model: this.config.model,
        messages,
        temperature: this.config.temperature,
        max_tokens: this.config.maxTokens
      };

      // 发送请求
      const response = await axios.post(apiUrl, requestBody, {
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json'
        }
      });

      // 处理DeepSeek API响应
      return response.data.choices[0].message.content;
    } catch (error) {
      console.error('LLM API请求错误:', error);

      // 处理不同类型的错误
      if (axios.isAxiosError(error) && error.response) {
        // API错误
        const statusCode = error.response.status;
        const errorMessage = error.response.data?.error?.message || 'Unknown API error';

        if (statusCode === 401) {
          throw new Error('API密钥无效，请检查您的API密钥');
        } else if (statusCode === 429) {
          throw new Error('API请求次数超限，请稍后再试');
        } else if (statusCode === 400) {
          throw new Error(`API请求错误: ${errorMessage}`);
        } else {
          throw new Error(`API错误 (${statusCode}): ${errorMessage}`);
        }
      }

      // 其他错误
      throw new Error('与AI服务通信失败，请检查网络连接或稍后再试');
    }
  }

  /**
   * 使用模拟数据（用于测试）
   * @param messages 消息历史
   * @returns Promise<string> 模拟的AI回复
   */
  async sendMessageMock(messages: Message[]): Promise<string> {
    // 模拟API延迟
    await new Promise(resolve => setTimeout(resolve, 1500));

    // 获取最后一条用户消息
    const lastUserMessage = messages.filter(m => m.role === 'user').pop();

    if (!lastUserMessage) {
      return "I don't see any message from you. Could you please say something?";
    }

    // 获取系统消息（角色设置）
    const systemMessage = messages.find(m => m.role === 'system');
    const role = systemMessage?.content || 'You are an English speaker and a friendly conversation partner.';

    // 根据用户消息内容生成更智能的回复
    const userContent = lastUserMessage.content.toLowerCase();

    // 检查是否是问候
    if (userContent.includes('hello') || userContent.includes('hi') || userContent.includes('hey')) {
      return "Hello! It's nice to talk with you. How can I help you practice English today? Would you like to discuss a specific topic or just have a casual conversation?";
    }

    // 检查是否询问天气
    else if (userContent.includes('weather')) {
      return "I don't have access to real-time weather information, but I'd be happy to discuss weather-related vocabulary with you. In English, we have many expressions about weather like 'It's raining cats and dogs' (heavy rain) or 'It's a scorcher' (very hot day). What kind of weather do you enjoy the most?";
    }

    // 检查是否询问名字
    else if (userContent.includes('name') || userContent.includes('call you')) {
      return "I'm your English practice assistant. You can call me whatever name makes you comfortable during our conversation. Some people call me Alex or Emma. What would you like to talk about today?";
    }

    // 检查是否询问如何学习英语
    else if (userContent.includes('learn english') || userContent.includes('improve') || userContent.includes('practice')) {
      return "That's a great question! To improve your English, consistent practice is key. Try to immerse yourself in English through movies, podcasts, or books. Speaking regularly with native speakers or language partners is also very effective. Would you like some specific resources or tips for a particular aspect of English you want to improve?";
    }

    // 检查是否询问旅行相关
    else if (userContent.includes('travel') || userContent.includes('visit') || userContent.includes('country')) {
      return "Traveling is a wonderful way to practice English! When visiting English-speaking countries, don't be afraid to make mistakes - most people appreciate your effort to speak their language. Have you traveled to any English-speaking countries before? Or is there a specific place you'd like to visit?";
    }

    // 检查是否询问工作或学习相关
    else if (userContent.includes('job') || userContent.includes('work') || userContent.includes('study') || userContent.includes('school')) {
      return "Discussing work or studies in English is excellent practice for professional settings. Business English has its own vocabulary and expressions that can be quite different from casual conversation. What field do you work in or study? I'd be happy to discuss some relevant vocabulary.";
    }

    // 检查是否询问偏好（喜欢的事物）
    else if (userContent.includes('favourite') || userContent.includes('favorite') || userContent.includes('like best') ||
             userContent.includes('prefer') || userContent.match(/who is your/i) || userContent.match(/what is your/i)) {

      // 检查具体询问的偏好类型
      if (userContent.includes('singer') || userContent.includes('music') || userContent.includes('song')) {
        return "I don't have personal preferences as an AI, but I can discuss music with you! Many English learners find that listening to songs helps improve their pronunciation and vocabulary. Some popular English-speaking artists include Taylor Swift, Ed Sheeran, Adele, and Beyoncé. Do you have a favorite singer or music genre?";
      }
      else if (userContent.includes('movie') || userContent.includes('film') || userContent.includes('actor') || userContent.includes('actress')) {
        return "As an AI, I don't watch movies, but I can certainly discuss them! Movies are a great way to practice English listening skills. Some classic English-language films include 'The Shawshank Redemption', 'Forrest Gump', and more recent ones like 'La La Land' or 'Parasite' (which won an Oscar despite not being in English). What kinds of movies do you enjoy watching?";
      }
      else if (userContent.includes('book') || userContent.includes('author') || userContent.includes('novel')) {
        return "While I don't have personal preferences, many English learners enjoy books by authors like J.K. Rowling (Harry Potter series), George Orwell (1984), or Ernest Hemingway (The Old Man and the Sea). Reading books in English is an excellent way to expand your vocabulary. What kind of books do you enjoy reading?";
      }
      else if (userContent.includes('food') || userContent.includes('dish') || userContent.includes('cuisine')) {
        return "I don't eat food, but I can tell you that English-speaking countries have diverse cuisines! American food includes hamburgers and apple pie, British cuisine features fish and chips and Sunday roast, while Australian food might include vegemite sandwiches and meat pies. What's your favorite food? I'd love to learn about cuisines from your culture!";
      }
      else if (userContent.includes('color') || userContent.includes('colour')) {
        return "I don't have a favorite color, but discussing colors is great for vocabulary practice! In English, we have basic colors like red, blue, green, but also more specific ones like turquoise, maroon, or chartreuse. Colors often appear in idioms too, like 'feeling blue' (sad) or 'seeing red' (angry). What's your favorite color?";
      }
      else if (userContent.includes('sport') || userContent.includes('team') || userContent.includes('athlete')) {
        return "I don't follow sports, but many English-speaking countries are passionate about different sports! Americans love basketball, baseball, and American football. The British enjoy football (soccer), cricket, and rugby. Canadians are known for ice hockey. Sports vocabulary is very useful for conversations with native speakers. Do you play or follow any sports?";
      }
      else {
        return "That's an interesting question about preferences! As an AI language assistant, I don't have personal favorites, but I'm happy to discuss various topics to help you practice English. Could you tell me about your favorites instead? That would give us a great topic to discuss and help you practice expressing your opinions in English.";
      }
    }

    // 检查是否是短消息
    else if (userContent.length < 15) {
      return "Could you elaborate a bit more? I'd love to have a more detailed conversation to help you practice your English. The more you express yourself, the more opportunities you have to improve your language skills.";
    }

    // 默认回复
    else {
      return "That's an interesting point! Your English expression is quite good. To expand on this topic, I'd like to ask: " +
             this.generateFollowUpQuestion(userContent) +
             " Remember, the more we converse, the more you'll improve your English fluency.";
    }
  }

  /**
   * 根据用户消息生成跟进问题
   * @param userContent 用户消息内容
   * @returns string 跟进问题
   */
  private generateFollowUpQuestion(userContent: string): string {
    const followUpQuestions = [
      "What do you think about this topic from a different perspective?",
      "Could you share a personal experience related to this?",
      "How does this compare to your own culture or background?",
      "What aspects of this topic would you like to explore further?",
      "Have you considered how this might change in the future?",
      "What's your opinion on how this affects everyday life?",
      "Would you like to learn more specific vocabulary related to this topic?",
      "How comfortable do you feel discussing this topic in English?",
      "What other related topics are you interested in exploring?"
    ];

    // 简单随机选择一个问题
    const randomIndex = Math.floor(Math.random() * followUpQuestions.length);
    return followUpQuestions[randomIndex];
  }

  /**
   * 设置LLM配置
   * @param config 新的配置
   */
  setConfig(config: Partial<LLMConfig>): void {
    this.config = { ...this.config, ...config };
  }

  /**
   * 获取当前LLM配置
   * @returns LLMConfig 当前配置
   */
  getConfig(): LLMConfig {
    return { ...this.config };
  }
}
