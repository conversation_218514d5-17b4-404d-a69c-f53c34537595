{"version": 3, "names": ["_features", "require", "generateRegexpuOptions", "pattern", "toTransform", "feat", "name", "hasFeature", "FEATURES", "featDuplicateNamedGroups", "regex", "seen", "Set", "match", "exec", "add", "has", "unicodeFlag", "unicodeSetsFlag", "dotAllFlag", "unicodePropertyEscapes", "namedGroups", "onNamedGroup", "modifiers", "canSkipRegexpu", "node", "options", "flags", "includes", "test", "transformFlags", "regexpuOptions", "replace"], "sources": ["../src/util.ts"], "sourcesContent": ["import type { types as t } from \"@babel/core\";\nimport { FEATURES, hasFeature } from \"./features.ts\";\n\nimport type { RegexpuOptions } from \"regexpu-core\";\n\nexport function generateRegexpuOptions(\n  pattern: string,\n  toTransform: number,\n): RegexpuOptions {\n  const feat = (name: keyof typeof FEATURES) => {\n    return hasFeature(toTransform, FEATURES[name]) ? \"transform\" : false;\n  };\n\n  const featDuplicateNamedGroups = (): \"transform\" | false => {\n    if (!feat(\"duplicateNamedCaptureGroups\")) return false;\n\n    // This can return false positive, for example for /\\(?<a>\\)/.\n    // However, it's such a rare occurrence that it's ok to compile\n    // the regexp even if we only need to compile regexps with\n    // duplicate named capturing groups.\n    // The $ is to exit early for malicious input such as \\(?<\\(?<\\(?<...\n    const regex = /\\(\\?<([^>]+)(>|$)/g;\n    const seen = new Set();\n    for (\n      let match;\n      (match = regex.exec(pattern)) && match[2];\n      seen.add(match[1])\n    ) {\n      if (seen.has(match[1])) return \"transform\";\n    }\n    return false;\n  };\n\n  return {\n    unicodeFlag: feat(\"unicodeFlag\"),\n    unicodeSetsFlag: feat(\"unicodeSetsFlag\"),\n    dotAllFlag: feat(\"dotAllFlag\"),\n    unicodePropertyEscapes: feat(\"unicodePropertyEscape\"),\n    namedGroups: feat(\"namedCaptureGroups\") || featDuplicateNamedGroups(),\n    onNamedGroup: () => {},\n    modifiers: feat(\"modifiers\"),\n  };\n}\n\nexport function canSkipRegexpu(\n  node: t.RegExpLiteral,\n  options: RegexpuOptions,\n): boolean {\n  const { flags, pattern } = node;\n\n  if (flags.includes(\"v\")) {\n    if (options.unicodeSetsFlag === \"transform\") return false;\n  }\n\n  if (flags.includes(\"u\")) {\n    if (options.unicodeFlag === \"transform\") return false;\n    if (\n      options.unicodePropertyEscapes === \"transform\" &&\n      /\\\\p\\{/i.test(pattern)\n    ) {\n      return false;\n    }\n  }\n\n  if (flags.includes(\"s\")) {\n    if (options.dotAllFlag === \"transform\") return false;\n  }\n\n  if (options.namedGroups === \"transform\" && /\\(\\?<(?![=!])/.test(pattern)) {\n    return false;\n  }\n\n  if (options.modifiers === \"transform\" && /\\(\\?[\\w-]+:/.test(pattern)) {\n    return false;\n  }\n\n  return true;\n}\n\nexport function transformFlags(regexpuOptions: RegexpuOptions, flags: string) {\n  if (regexpuOptions.unicodeSetsFlag === \"transform\") {\n    flags = flags.replace(\"v\", \"u\");\n  }\n  if (regexpuOptions.unicodeFlag === \"transform\") {\n    flags = flags.replace(\"u\", \"\");\n  }\n  if (regexpuOptions.dotAllFlag === \"transform\") {\n    flags = flags.replace(\"s\", \"\");\n  }\n  return flags;\n}\n"], "mappings": ";;;;;;;;AACA,IAAAA,SAAA,GAAAC,OAAA;AAIO,SAASC,sBAAsBA,CACpCC,OAAe,EACfC,WAAmB,EACH;EAChB,MAAMC,IAAI,GAAIC,IAA2B,IAAK;IAC5C,OAAO,IAAAC,oBAAU,EAACH,WAAW,EAAEI,kBAAQ,CAACF,IAAI,CAAC,CAAC,GAAG,WAAW,GAAG,KAAK;EACtE,CAAC;EAED,MAAMG,wBAAwB,GAAGA,CAAA,KAA2B;IAC1D,IAAI,CAACJ,IAAI,CAAC,6BAA6B,CAAC,EAAE,OAAO,KAAK;IAOtD,MAAMK,KAAK,GAAG,oBAAoB;IAClC,MAAMC,IAAI,GAAG,IAAIC,GAAG,CAAC,CAAC;IACtB,KACE,IAAIC,KAAK,EACT,CAACA,KAAK,GAAGH,KAAK,CAACI,IAAI,CAACX,OAAO,CAAC,KAAKU,KAAK,CAAC,CAAC,CAAC,EACzCF,IAAI,CAACI,GAAG,CAACF,KAAK,CAAC,CAAC,CAAC,CAAC,EAClB;MACA,IAAIF,IAAI,CAACK,GAAG,CAACH,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,WAAW;IAC5C;IACA,OAAO,KAAK;EACd,CAAC;EAED,OAAO;IACLI,WAAW,EAAEZ,IAAI,CAAC,aAAa,CAAC;IAChCa,eAAe,EAAEb,IAAI,CAAC,iBAAiB,CAAC;IACxCc,UAAU,EAAEd,IAAI,CAAC,YAAY,CAAC;IAC9Be,sBAAsB,EAAEf,IAAI,CAAC,uBAAuB,CAAC;IACrDgB,WAAW,EAAEhB,IAAI,CAAC,oBAAoB,CAAC,IAAII,wBAAwB,CAAC,CAAC;IACrEa,YAAY,EAAEA,CAAA,KAAM,CAAC,CAAC;IACtBC,SAAS,EAAElB,IAAI,CAAC,WAAW;EAC7B,CAAC;AACH;AAEO,SAASmB,cAAcA,CAC5BC,IAAqB,EACrBC,OAAuB,EACd;EACT,MAAM;IAAEC,KAAK;IAAExB;EAAQ,CAAC,GAAGsB,IAAI;EAE/B,IAAIE,KAAK,CAACC,QAAQ,CAAC,GAAG,CAAC,EAAE;IACvB,IAAIF,OAAO,CAACR,eAAe,KAAK,WAAW,EAAE,OAAO,KAAK;EAC3D;EAEA,IAAIS,KAAK,CAACC,QAAQ,CAAC,GAAG,CAAC,EAAE;IACvB,IAAIF,OAAO,CAACT,WAAW,KAAK,WAAW,EAAE,OAAO,KAAK;IACrD,IACES,OAAO,CAACN,sBAAsB,KAAK,WAAW,IAC9C,QAAQ,CAACS,IAAI,CAAC1B,OAAO,CAAC,EACtB;MACA,OAAO,KAAK;IACd;EACF;EAEA,IAAIwB,KAAK,CAACC,QAAQ,CAAC,GAAG,CAAC,EAAE;IACvB,IAAIF,OAAO,CAACP,UAAU,KAAK,WAAW,EAAE,OAAO,KAAK;EACtD;EAEA,IAAIO,OAAO,CAACL,WAAW,KAAK,WAAW,IAAI,eAAe,CAACQ,IAAI,CAAC1B,OAAO,CAAC,EAAE;IACxE,OAAO,KAAK;EACd;EAEA,IAAIuB,OAAO,CAACH,SAAS,KAAK,WAAW,IAAI,aAAa,CAACM,IAAI,CAAC1B,OAAO,CAAC,EAAE;IACpE,OAAO,KAAK;EACd;EAEA,OAAO,IAAI;AACb;AAEO,SAAS2B,cAAcA,CAACC,cAA8B,EAAEJ,KAAa,EAAE;EAC5E,IAAII,cAAc,CAACb,eAAe,KAAK,WAAW,EAAE;IAClDS,KAAK,GAAGA,KAAK,CAACK,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;EACjC;EACA,IAAID,cAAc,CAACd,WAAW,KAAK,WAAW,EAAE;IAC9CU,KAAK,GAAGA,KAAK,CAACK,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC;EAChC;EACA,IAAID,cAAc,CAACZ,UAAU,KAAK,WAAW,EAAE;IAC7CQ,KAAK,GAAGA,KAAK,CAACK,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC;EAChC;EACA,OAAOL,KAAK;AACd", "ignoreList": []}