{"version": 3, "names": ["_typeof", "obj", "Symbol", "iterator", "exports", "default", "constructor", "prototype"], "sources": ["../../src/helpers/typeof.ts"], "sourcesContent": ["/* @minVersion 7.0.0-beta.0 */\n\nexport default function _typeof(\n  obj: unknown,\n):\n  | \"string\"\n  | \"number\"\n  | \"bigint\"\n  | \"boolean\"\n  | \"symbol\"\n  | \"undefined\"\n  | \"object\"\n  | \"function\" {\n  \"@babel/helpers - typeof\";\n\n  if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") {\n    // @ts-expect-error -- deliberate re-defining typeof\n    _typeof = function (obj: unknown) {\n      return typeof obj;\n    };\n  } else {\n    // @ts-expect-error -- deliberate re-defining typeof\n    _typeof = function (obj: unknown) {\n      return obj &&\n        typeof Symbol === \"function\" &&\n        obj.constructor === Symbol &&\n        obj !== Symbol.prototype\n        ? \"symbol\"\n        : typeof obj;\n    };\n  }\n\n  return _typeof(obj);\n}\n"], "mappings": ";;;;;;AAEe,SAASA,OAAOA,CAC7BC,GAAY,EASC;EACb,yBAAyB;;EAEzB,IAAI,OAAOC,MAAM,KAAK,UAAU,IAAI,OAAOA,MAAM,CAACC,QAAQ,KAAK,QAAQ,EAAE;IAEvEC,OAAA,CAAAC,OAAA,GAAAL,OAAO,GAAG,SAAAA,CAAUC,GAAY,EAAE;MAChC,OAAO,OAAOA,GAAG;IACnB,CAAC;EACH,CAAC,MAAM;IAELG,OAAA,CAAAC,OAAA,GAAAL,OAAO,GAAG,SAAAA,CAAUC,GAAY,EAAE;MAChC,OAAOA,GAAG,IACR,OAAOC,MAAM,KAAK,UAAU,IAC5BD,GAAG,CAACK,WAAW,KAAKJ,MAAM,IAC1BD,GAAG,KAAKC,MAAM,CAACK,SAAS,GACtB,QAAQ,GACR,OAAON,GAAG;IAChB,CAAC;EACH;EAEA,OAAOD,OAAO,CAACC,GAAG,CAAC;AACrB", "ignoreList": []}