{"version": 3, "names": ["_applyDecoratedDescriptor", "target", "property", "decorators", "descriptor", "context", "desc", "Object", "keys", "for<PERSON>ach", "key", "enumerable", "configurable", "initializer", "writable", "slice", "reverse", "reduce", "decorator", "value", "call", "defineProperty"], "sources": ["../../src/helpers/applyDecoratedDescriptor.ts"], "sourcesContent": ["/* @minVersion 7.0.0-beta.0 */\n\ninterface DescriptorWithInitializer extends PropertyDescriptor {\n  initializer?: () => any;\n}\n\ndeclare const Object: Omit<typeof globalThis.Object, \"keys\"> & {\n  keys<T>(o: T): Array<keyof T>;\n};\n\nexport default function _applyDecoratedDescriptor<T>(\n  target: T,\n  property: PropertyKey,\n  decorators: ((\n    t: T,\n    p: PropertyKey,\n    desc: DescriptorWithInitializer,\n  ) => any)[],\n  descriptor: DescriptorWithInitializer,\n  context: DecoratorContext,\n) {\n  var desc: DescriptorWithInitializer = {};\n  Object.keys(descriptor).forEach(function (key) {\n    desc[key] = descriptor[key];\n  });\n  desc.enumerable = !!desc.enumerable;\n  desc.configurable = !!desc.configurable;\n  if (\"value\" in desc || desc.initializer) {\n    desc.writable = true;\n  }\n\n  desc = decorators\n    .slice()\n    .reverse()\n    .reduce(function (desc, decorator) {\n      return decorator(target, property, desc) || desc;\n    }, desc);\n\n  if (context && desc.initializer !== void 0) {\n    desc.value = desc.initializer ? desc.initializer.call(context) : void 0;\n    desc.initializer = void 0;\n  }\n\n  if (desc.initializer === void 0) {\n    Object.defineProperty(target, property, desc);\n    return null;\n  }\n\n  return desc;\n}\n"], "mappings": ";;;;;;AAUe,SAASA,yBAAyBA,CAC/CC,MAAS,EACTC,QAAqB,EACrBC,UAIW,EACXC,UAAqC,EACrCC,OAAyB,EACzB;EACA,IAAIC,IAA+B,GAAG,CAAC,CAAC;EACxCC,MAAM,CAACC,IAAI,CAACJ,UAAU,CAAC,CAACK,OAAO,CAAC,UAAUC,GAAG,EAAE;IAC7CJ,IAAI,CAACI,GAAG,CAAC,GAAGN,UAAU,CAACM,GAAG,CAAC;EAC7B,CAAC,CAAC;EACFJ,IAAI,CAACK,UAAU,GAAG,CAAC,CAACL,IAAI,CAACK,UAAU;EACnCL,IAAI,CAACM,YAAY,GAAG,CAAC,CAACN,IAAI,CAACM,YAAY;EACvC,IAAI,OAAO,IAAIN,IAAI,IAAIA,IAAI,CAACO,WAAW,EAAE;IACvCP,IAAI,CAACQ,QAAQ,GAAG,IAAI;EACtB;EAEAR,IAAI,GAAGH,UAAU,CACdY,KAAK,CAAC,CAAC,CACPC,OAAO,CAAC,CAAC,CACTC,MAAM,CAAC,UAAUX,IAAI,EAAEY,SAAS,EAAE;IACjC,OAAOA,SAAS,CAACjB,MAAM,EAAEC,QAAQ,EAAEI,IAAI,CAAC,IAAIA,IAAI;EAClD,CAAC,EAAEA,IAAI,CAAC;EAEV,IAAID,OAAO,IAAIC,IAAI,CAACO,WAAW,KAAK,KAAK,CAAC,EAAE;IAC1CP,IAAI,CAACa,KAAK,GAAGb,IAAI,CAACO,WAAW,GAAGP,IAAI,CAACO,WAAW,CAACO,IAAI,CAACf,OAAO,CAAC,GAAG,KAAK,CAAC;IACvEC,IAAI,CAACO,WAAW,GAAG,KAAK,CAAC;EAC3B;EAEA,IAAIP,IAAI,CAACO,WAAW,KAAK,KAAK,CAAC,EAAE;IAC/BN,MAAM,CAACc,cAAc,CAACpB,MAAM,EAAEC,QAAQ,EAAEI,IAAI,CAAC;IAC7C,OAAO,IAAI;EACb;EAEA,OAAOA,IAAI;AACb", "ignoreList": []}