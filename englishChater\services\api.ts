import axios, { isAxiosError } from 'axios';
import * as FileSystem from 'expo-file-system';
import * as crypto from 'expo-crypto';
import { Audio } from 'expo-av';
import { ApiConfigService } from './apiConfig';

// OpenAI Whisper API配置
const WHISPER_API_URL = 'https://api.openai.com/v1/audio/transcriptions';

/**
 * 语音识别服务类
 */
export class SpeechRecognitionService {
  // 百度语音识别访问令牌
  private static baiduAccessToken: string = '';
  private static tokenExpireTime: number = 0;
  private static deviceCuid: string = '';

  /**
   * 清除百度访问令牌缓存
   * 当API密钥更新时需要调用此方法
   */
  static clearBaiduTokenCache(): void {
    console.log('清除百度访问令牌缓存');
    this.baiduAccessToken = '';
    this.tokenExpireTime = 0;
  }

  /**
   * 生成设备唯一标识符（CUID）
   * @returns Promise<string> 设备唯一标识符
   */
  static async generateDeviceCuid(): Promise<string> {
    if (this.deviceCuid) {
      return this.deviceCuid;
    }

    try {
      // 尝试从存储中获取已保存的CUID
      const storedCuid = await FileSystem.readAsStringAsync(
        FileSystem.documentDirectory + 'device_cuid.txt'
      ).catch(() => null);

      if (storedCuid) {
        this.deviceCuid = storedCuid;
        return this.deviceCuid;
      }

      // 生成新的CUID（使用时间戳和随机数）
      const timestamp = Date.now().toString();
      const random = Math.random().toString(36).substring(2, 15);
      this.deviceCuid = `english_app_${timestamp}_${random}`;

      // 保存到本地存储
      await FileSystem.writeAsStringAsync(
        FileSystem.documentDirectory + 'device_cuid.txt',
        this.deviceCuid
      );

      return this.deviceCuid;
    } catch (error) {
      console.error('生成设备CUID失败:', error);
      // 回退到简单的随机字符串
      this.deviceCuid = `english_app_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
      return this.deviceCuid;
    }
  }

  /**
   * 将音频文件转换为base64编码（提取纯PCM数据）
   * 百度API需要纯PCM数据，不包含WAV文件头
   * @param uri 音频文件URI
   * @returns Promise<{base64Audio: string, pcmDataLength: number}> base64编码的PCM数据和长度
   */
  static async convertAudioToBase64(uri: string): Promise<{base64Audio: string, pcmDataLength: number}> {
    try {
      console.log('开始转换音频文件为base64（提取PCM数据）...');

      // 获取文件信息
      const fileInfo = await FileSystem.getInfoAsync(uri);
      if (!fileInfo.exists) {
        throw new Error('音频文件不存在');
      }

      console.log(`音频文件大小: ${fileInfo.size} 字节`);

      // 检查文件大小
      if (fileInfo.size > 10 * 1024 * 1024) { // 10MB
        throw new Error('音频文件过大，请录制更短的音频');
      }

      // 读取完整文件内容为base64
      const fullBase64Audio = await FileSystem.readAsStringAsync(uri, {
        encoding: FileSystem.EncodingType.Base64,
      });

      console.log(`完整文件base64长度: ${fullBase64Audio.length}`);

      // 清理base64字符串
      let cleanBase64 = fullBase64Audio
        .replace(/^data:.*?;base64,/, '')  // 移除data URL前缀
        .replace(/^data:.*?,/, '')         // 移除其他data前缀
        .replace(/[\r\n\s]/g, '');         // 移除所有空白字符

      // 检查文件格式
      const fileExt = uri.split('.').pop()?.toLowerCase() || 'm4a';

      console.log(`检测到${fileExt.toUpperCase()}文件，百度API支持此格式`);

      // 百度API支持m4a格式，直接使用完整数据
      const dataLength = Math.floor(cleanBase64.length * 3 / 4);

      console.log(`音频文件格式: ${fileExt}`);
      console.log(`完整文件大小: ${dataLength}字节`);

      return {
        base64Audio: cleanBase64,
        pcmDataLength: dataLength
      };
    } catch (error) {
      console.error('转换音频为base64错误:', error);
      throw error;
    }
  }

  /**
   * 使用 AK，SK 生成鉴权签名（Access Token）
   * 完全按照百度官方示例代码实现
   * @return Promise<string> 鉴权签名信息（Access Token）
   */
  static async getBaiduAccessToken(): Promise<string> {
    // 检查是否已有有效的令牌
    const now = Date.now();
    if (this.baiduAccessToken && now < this.tokenExpireTime) {
      console.log('使用缓存的百度访问令牌');
      return this.baiduAccessToken;
    }

    try {
      console.log('获取新的百度访问令牌...');

      // 从配置服务获取API密钥
      const config = await ApiConfigService.getApiConfig();
      const { baiduApiKey, baiduSecretKey } = config;

      // 添加调试信息（不显示完整密钥，只显示前几位和长度）
      console.log('百度API密钥信息:');
      console.log(`- API Key: ${baiduApiKey.substring(0, 8)}... (长度: ${baiduApiKey.length})`);
      console.log(`- Secret Key: ${baiduSecretKey.substring(0, 8)}... (长度: ${baiduSecretKey.length})`);

      // 验证密钥格式
      if (!baiduApiKey || baiduApiKey.length < 10) {
        throw new Error('百度API Key格式不正确或为空');
      }
      if (!baiduSecretKey || baiduSecretKey.length < 10) {
        throw new Error('百度Secret Key格式不正确或为空');
      }

      // 完全按照百度官方示例构建URL
      const url = `https://aip.baidubce.com/oauth/2.0/token?grant_type=client_credentials&client_id=${baiduApiKey}&client_secret=${baiduSecretKey}`;

      // 使用POST方法获取token，完全按照官方示例
      const options = {
        method: 'POST',
        url: url,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      };

      console.log('发送token请求...');
      const response = await axios(options);
      console.log('收到token响应:', response.status);

      if (response.data && response.data.access_token) {
        console.log('成功获取百度访问令牌');
        this.baiduAccessToken = response.data.access_token;
        // 设置令牌过期时间（提前5分钟过期，以防万一）
        this.tokenExpireTime = now + (response.data.expires_in - 300) * 1000;
        return this.baiduAccessToken;
      } else {
        console.error('百度访问令牌响应格式错误:', response.data);
        throw new Error('获取百度访问令牌失败: 响应格式错误');
      }
    } catch (error) {
      console.error('获取百度访问令牌错误:', error);
      if (isAxiosError(error) && error.response) {
        console.error('百度API响应:', error.response.data);
        throw new Error(`获取百度访问令牌失败: ${error.response.status} - ${JSON.stringify(error.response.data)}`);
      }
      throw new Error('获取百度语音识别访问令牌失败，请检查API密钥和网络连接');
    }
  }

  /**
   * 获取音频文件的实际时长
   * @param audioUri 音频文件URI
   * @returns Promise<number> 音频时长（秒）
   */
  static async getActualAudioDuration(audioUri: string): Promise<number> {
    try {
      console.log(`尝试获取音频时长: ${audioUri}`);

      // 使用Expo Audio API获取真实的音频时长
      const { sound } = await Audio.Sound.createAsync({ uri: audioUri });
      console.log('音频Sound对象创建成功');

      const status = await sound.getStatusAsync();
      console.log('音频状态:', status);

      await sound.unloadAsync(); // 释放资源
      console.log('音频资源已释放');

      // 检查状态类型并获取时长
      if (status.isLoaded && 'durationMillis' in status && status.durationMillis) {
        const durationSeconds = status.durationMillis / 1000;
        console.log(`实际音频时长: ${durationSeconds}秒 (${status.durationMillis}毫秒)`);
        return durationSeconds;
      } else {
        console.log('无法从音频状态获取时长，状态详情:', {
          isLoaded: status.isLoaded,
          hasDurationMillis: 'durationMillis' in status,
          durationMillis: 'durationMillis' in status ? (status as any).durationMillis : 'N/A'
        });
        console.log('回退到文件大小估算方法');
        return this.estimateAudioDurationFromFileSize(audioUri);
      }
    } catch (error) {
      console.error('获取音频时长失败:', error);
      console.log('回退到文件大小估算方法');
      // 如果无法获取实际时长，回退到估算方法
      return this.estimateAudioDurationFromFileSize(audioUri);
    }
  }

  /**
   * 基于文件大小估算音频时长（备用方法）
   * @param audioUri 音频文件URI
   * @returns Promise<number> 估算的音频时长（秒）
   */
  static async estimateAudioDurationFromFileSize(audioUri: string): Promise<number> {
    try {
      const fileInfo = await FileSystem.getInfoAsync(audioUri);
      const fileExt = audioUri.split('.').pop()?.toLowerCase() || 'wav';

      // 检查文件是否存在并且有大小信息
      if (!fileInfo.exists || !('size' in fileInfo)) {
        console.log('文件不存在或无法获取大小信息');
        return 0.1;
      }

      const fileSize = fileInfo.size;
      console.log(`文件大小: ${fileSize}字节, 扩展名: ${fileExt}`);

      // 对于不同格式使用不同的估算方法
      let estimatedDuration = 0;

      if (fileExt === 'wav') {
        // WAV文件：考虑文件头和可能的压缩
        // 实际的WAV文件头可能比44字节大，特别是包含元数据时
        const headerSize = Math.min(fileSize * 0.1, 1024); // 假设文件头不超过文件大小的10%或1KB
        const dataSize = Math.max(0, fileSize - headerSize);

        // 考虑可能的压缩，使用更保守的估算
        // 对于DEFAULT格式，通常不是原始PCM，而是某种压缩格式
        const bytesPerSecond = 8000; // 保守估算，考虑压缩
        estimatedDuration = dataSize / bytesPerSecond;

        console.log(`WAV文件估算: 文件头${headerSize}字节, 数据${dataSize}字节, 估算时长${estimatedDuration}秒`);
      } else {
        // 其他格式的保守估算
        const bytesPerSecond = 4000; // 非常保守的估算
        estimatedDuration = fileSize / bytesPerSecond;

        console.log(`${fileExt}文件估算: ${fileSize}字节, 估算时长${estimatedDuration}秒`);
      }

      return Math.max(0.1, estimatedDuration); // 最小0.1秒
    } catch (error) {
      console.error('估算音频时长失败:', error);
      return 0.1; // 默认返回0.1秒
    }
  }

  /**
   * 分析WAV文件结构（改进版）
   * @param base64Audio base64编码的音频数据
   * @returns Promise<{pcmDataLength?: number, headerSize?: number, format?: string}> WAV文件分析结果
   */
  static async analyzeWavFile(base64Audio: string): Promise<{pcmDataLength?: number, headerSize?: number, format?: string}> {
    try {
      // 解码base64的前200字节来分析文件头（增加分析范围）
      const headerBase64 = base64Audio.substring(0, 268); // 约200字节的base64
      const headerBytes = this.base64ToBytes(headerBase64);

      console.log(`WAV文件分析: 分析前${headerBytes.length}字节`);

      // 检查WAV文件签名
      const riffSignature = String.fromCharCode(...headerBytes.slice(0, 4));
      const waveSignature = String.fromCharCode(...headerBytes.slice(8, 12));

      console.log(`文件签名: RIFF="${riffSignature}", WAVE="${waveSignature}"`);

      if (riffSignature !== 'RIFF' || waveSignature !== 'WAVE') {
        console.log('不是标准WAV文件格式，可能是其他音频格式');

        // 尝试检查是否是其他音频格式
        const firstBytes = headerBytes.slice(0, 8);
        const hexString = Array.from(firstBytes).map(b => b.toString(16).padStart(2, '0')).join(' ');
        console.log(`文件头十六进制: ${hexString}`);

        return { format: 'unknown' };
      }

      // 读取文件大小（RIFF chunk size）
      const fileSize = this.bytesToInt32(headerBytes.slice(4, 8)) + 8;
      console.log(`RIFF文件大小: ${fileSize}字节`);

      // 查找fmt和data chunk
      let offset = 12;
      let dataChunkSize = 0;
      let headerSize = 44; // 默认值
      let fmtChunkFound = false;

      while (offset < headerBytes.length - 8) {
        const chunkId = String.fromCharCode(...headerBytes.slice(offset, offset + 4));
        const chunkSize = this.bytesToInt32(headerBytes.slice(offset + 4, offset + 8));

        console.log(`发现chunk: "${chunkId}", 大小: ${chunkSize}字节, 偏移: ${offset}`);

        if (chunkId === 'fmt ') {
          fmtChunkFound = true;
          // 可以进一步分析fmt chunk获取音频格式信息
        } else if (chunkId === 'data') {
          dataChunkSize = chunkSize;
          headerSize = offset + 8; // data chunk开始位置
          console.log(`找到data chunk: 大小=${dataChunkSize}字节, 文件头大小=${headerSize}字节`);
          break;
        }

        offset += 8 + chunkSize;

        // 防止无限循环
        if (offset >= headerBytes.length) {
          console.log('已到达分析范围末尾');
          break;
        }
      }

      console.log(`WAV文件分析完成: fmt chunk=${fmtChunkFound ? '找到' : '未找到'}, data chunk大小=${dataChunkSize}`);

      return {
        pcmDataLength: dataChunkSize > 0 ? dataChunkSize : undefined,
        headerSize: headerSize,
        format: 'wav'
      };
    } catch (error) {
      console.error('WAV文件分析失败:', error);
      return { format: 'error' };
    }
  }

  /**
   * 将base64字符串转换为字节数组
   * @param base64 base64字符串
   * @returns Uint8Array 字节数组
   */
  static base64ToBytes(base64: string): Uint8Array {
    const binaryString = atob(base64);
    const bytes = new Uint8Array(binaryString.length);
    for (let i = 0; i < binaryString.length; i++) {
      bytes[i] = binaryString.charCodeAt(i);
    }
    return bytes;
  }

  /**
   * 将4字节数组转换为32位整数（小端序）
   * @param bytes 4字节数组
   * @returns number 32位整数
   */
  static bytesToInt32(bytes: Uint8Array): number {
    return bytes[0] | (bytes[1] << 8) | (bytes[2] << 16) | (bytes[3] << 24);
  }

  /**
   * 估算音频时长（兼容性方法）
   * @param fileSize 文件大小（字节）
   * @param fileExt 文件扩展名
   * @returns Promise<number> 估算的音频时长（秒）
   */
  static async estimateAudioDuration(fileSize: number, fileExt: string): Promise<number> {
    // 这个方法保留用于向后兼容，但使用更保守的估算
    console.log(`兼容性估算: 文件大小${fileSize}字节, 扩展名${fileExt}`);

    if (fileExt === 'wav') {
      // 对于WAV文件，使用更保守的估算
      const headerSize = Math.min(fileSize * 0.1, 1024);
      const dataSize = Math.max(0, fileSize - headerSize);
      const bytesPerSecond = 8000; // 保守估算
      const estimatedDuration = dataSize / bytesPerSecond;

      console.log(`兼容性WAV估算: 数据${dataSize}字节, 估算时长${estimatedDuration}秒`);
      return Math.max(0.1, estimatedDuration);
    } else {
      // 其他格式
      const bytesPerSecond = 4000;
      const estimatedDuration = fileSize / bytesPerSecond;

      console.log(`兼容性${fileExt}估算: 估算时长${estimatedDuration}秒`);
      return Math.max(0.1, estimatedDuration);
    }
  }

  /**
   * 检查音频文件质量（使用预先获取的时长）
   * @param audioUri 音频文件URI
   * @param actualDuration 实际音频时长（秒）
   * @returns Promise<{isValid: boolean; message?: string}> 是否符合质量要求
   */
  static async checkAudioQualityWithDuration(audioUri: string, actualDuration: number): Promise<{ isValid: boolean; message?: string }> {
    try {
      console.log(`=== 开始音频质量检查（使用预设时长）: ${audioUri} ===`);

      // 获取文件信息
      const fileInfo = await FileSystem.getInfoAsync(audioUri);
      console.log('文件信息详情:', fileInfo);

      if (!fileInfo.exists) {
        return { isValid: false, message: '音频文件不存在' };
      }

      // 检查文件大小（至少1KB，最多10MB）
      if ('size' in fileInfo) {
        console.log(`文件大小: ${fileInfo.size}字节 (${(fileInfo.size / 1024).toFixed(2)}KB)`);

        if (fileInfo.size < 1024) {
          return { isValid: false, message: '音频文件太小，可能录音失败' };
        }

        if (fileInfo.size > 10 * 1024 * 1024) {
          return { isValid: false, message: '音频文件过大，请录制更短的音频' };
        }
      }

      // 使用预先获取的音频时长
      console.log(`音频质量检查: 使用预设时长 ${actualDuration}秒`);

      // 检查音频时长
      if (actualDuration < 0.5) {
        console.log(`时长检查失败: ${actualDuration}秒 < 0.5秒`);
        return { isValid: false, message: '录音时间太短，请录制至少2-3秒的音频' };
      }

      if (actualDuration > 60) {
        console.log(`时长检查失败: ${actualDuration}秒 > 60秒`);
        return { isValid: false, message: '录音时间太长，请录制不超过60秒的音频' };
      }

      console.log(`音频质量检查通过: 时长${actualDuration}秒`);
      return { isValid: true };
    } catch (error) {
      console.error('音频质量检查失败:', error);
      return { isValid: false, message: `音频质量检查失败: ${error}` };
    }
  }

  /**
   * 检查音频文件质量（改进版本，使用实际音频时长）
   * @param audioUri 音频文件URI
   * @returns Promise<{isValid: boolean; message?: string}> 是否符合质量要求
   */
  static async checkAudioQualityAdvanced(audioUri: string): Promise<{ isValid: boolean; message?: string }> {
    try {
      console.log(`=== 开始音频质量检查: ${audioUri} ===`);

      // 获取文件信息
      const fileInfo = await FileSystem.getInfoAsync(audioUri);
      console.log('文件信息详情:', fileInfo);

      if (!fileInfo.exists) {
        return { isValid: false, message: '音频文件不存在' };
      }

      // 检查文件大小（至少1KB，最多10MB）
      if ('size' in fileInfo) {
        console.log(`文件大小: ${fileInfo.size}字节 (${(fileInfo.size / 1024).toFixed(2)}KB)`);

        if (fileInfo.size < 1024) {
          return { isValid: false, message: '音频文件太小，可能录音失败' };
        }

        if (fileInfo.size > 10 * 1024 * 1024) {
          return { isValid: false, message: '音频文件过大，请录制更短的音频' };
        }
      }

      // 获取实际音频时长
      console.log('开始获取实际音频时长...');
      const actualDuration = await this.getActualAudioDuration(audioUri);
      console.log(`音频质量检查: 实际时长 ${actualDuration}秒`);

      // 检查音频时长
      if (actualDuration < 0.5) {
        console.log(`时长检查失败: ${actualDuration}秒 < 0.5秒`);
        return { isValid: false, message: '录音时间太短，请录制至少2-3秒的音频' };
      }

      if (actualDuration > 60) {
        console.log(`时长检查失败: ${actualDuration}秒 > 60秒`);
        return { isValid: false, message: '录音时间太长，请录制不超过60秒的音频' };
      }

      console.log(`音频质量检查通过: 时长${actualDuration}秒`);
      return { isValid: true };
    } catch (error) {
      console.error('音频质量检查失败:', error);
      return { isValid: false, message: `音频质量检查失败: ${error}` };
    }
  }

  /**
   * 检查音频文件质量（兼容性版本）
   * @param fileInfo 文件信息
   * @returns boolean 是否符合质量要求
   */
  static checkAudioQuality(fileInfo: any): { isValid: boolean; message?: string } {
    // 检查文件大小（至少1KB，最多10MB）
    if (!('size' in fileInfo)) {
      return { isValid: false, message: '无法获取文件大小信息' };
    }

    if (fileInfo.size < 1024) {
      return { isValid: false, message: '音频文件太小，可能录音失败' };
    }

    if (fileInfo.size > 10 * 1024 * 1024) {
      return { isValid: false, message: '音频文件过大，请录制更短的音频' };
    }

    // 使用更保守的文件大小检查（不再依赖不准确的时长估算）
    // 对于现代压缩格式，1KB以上的文件通常包含有效音频
    console.log(`兼容性质量检查通过: 文件大小${fileInfo.size}字节`);
    return { isValid: true };
  }

  /**
   * 使用百度语音识别API进行语音识别
   * 完全按照百度官方示例代码实现
   * @param audioUri 音频文件URI
   * @returns Promise<string> 识别出的文本
   */
  static async recognizeSpeech(audioUri: string): Promise<string> {
    try {
      console.log('开始语音识别处理...');

      // 获取访问令牌
      const accessToken = await this.getBaiduAccessToken();
      console.log('成功获取访问令牌');

      // 获取音频文件信息
      const fileInfo = await FileSystem.getInfoAsync(audioUri);
      console.log('音频文件信息:', fileInfo);

      if (!fileInfo.exists) {
        throw new Error('音频文件不存在');
      }

      // 首先获取实际音频时长（只调用一次）
      const actualDuration = await this.getActualAudioDuration(audioUri);
      console.log(`=== 音频时长确认: ${actualDuration}秒 ===`);

      // 使用改进的音频质量检查（传入已获取的时长，避免重复调用）
      const qualityCheck = await this.checkAudioQualityWithDuration(audioUri, actualDuration);
      if (!qualityCheck.isValid) {
        throw new Error(qualityCheck.message);
      }

      // 将音频转换为base64（提取纯PCM数据）
      const audioData = await this.convertAudioToBase64(audioUri);
      const { base64Audio, pcmDataLength } = audioData;
      console.log(`音频转换为base64成功，PCM数据长度: ${pcmDataLength}字节`);

      // 获取文件扩展名
      const fileExt = audioUri.split('.').pop()?.toLowerCase() || 'wav';
      console.log(`音频文件扩展名: ${fileExt}`);

      // 严格按照百度官方示例：使用M4A格式
      console.log('=== 严格按照百度官方示例设置参数 ===');

      // 百度API官方示例要求：
      // 1. format: "m4a" (百度支持的格式)
      // 2. rate: 16000 (固定值)
      // 3. channel: 1 (固定值，仅支持单声道)
      // 4. len: 原始音频文件的字节数

      const audioFormat = 'm4a';  // 百度支持的格式
      const sampleRate = 16000;   // 百度推荐采样率
      const finalDataLength = pcmDataLength; // 使用原始文件大小

      console.log('百度API官方参数:');
      console.log(`- format: "${audioFormat}" (百度支持)`);
      console.log(`- rate: ${sampleRate} (百度推荐)`);
      console.log(`- channel: 1 (百度要求)`);
      console.log(`- len: ${finalDataLength} (原始文件字节数)`);
      console.log(`- base64长度: ${base64Audio.length} (仅用于speech参数)`);
      console.log(`- 估算时长: ${(finalDataLength / (16000 * 2 * 1)).toFixed(2)}秒（仅供参考）`);

      // 检查音频时长限制（百度API限制60秒）
      const estimatedDuration = finalDataLength / (16000 * 2 * 1);
      if (estimatedDuration > 60) {
        throw new Error(`音频时长${estimatedDuration.toFixed(1)}秒超过百度API限制（60秒），请录制更短的音频`);
      }

      // 检查文件大小有效性
      if (finalDataLength <= 0) {
        throw new Error('音频文件大小无效，请重新录制');
      }

      // 生成设备唯一标识符
      const deviceCuid = await this.generateDeviceCuid();

      // 构建请求参数，严格按照百度API官方示例
      const requestData = {
        format: audioFormat,  // string 必填：语音文件的格式，pcm/wav/amr/m4a。推荐pcm文件
        rate: sampleRate,     // int 必填：采样率，16000、8000，固定值
        channel: 1,           // int 必填：声道数，仅支持单声道，请填写固定值 1
        cuid: deviceCuid,     // string 必填：用户唯一标识，用来区分用户，长度为60字符以内
        token: accessToken,   // string 必填：access_token鉴权信息
        speech: base64Audio,  // string 必填：本地语音文件的二进制语音数据，需要进行base64编码
        len: finalDataLength, // int 必填：本地语音文件的字节数，单位字节
        dev_pid: 1737         // int 选填：识别模型，1537为普通话输入法模型（默认），1737为英语模型
      };

      console.log('=== 发送请求到百度语音识别API ===');
      console.log('请求参数（百度官方格式）:', {
        format: requestData.format,
        rate: requestData.rate,
        channel: requestData.channel,
        cuid: requestData.cuid,
        len: requestData.len,
        dev_pid: requestData.dev_pid,
        speech_length: requestData.speech.length,
        token_length: requestData.token.length
      });

      // 发送请求到百度语音识别API，严格按照百度官方示例
      // 官方示例：POST http://vop.baidu.com/server_api
      // 固定头部 header: Content-Type:application/json
      const options = {
        method: 'POST',
        url: 'http://vop.baidu.com/server_api', // 百度官方示例URL
        headers: {
          'Content-Type': 'application/json'    // 百度官方要求的固定头部
        },
        data: requestData
      };

      const response = await axios(options);
      console.log('百度语音识别API响应状态码:', response.status);
      console.log('百度语音识别API响应:', response.data);

      // 处理响应
      if (response.data && response.data.result && response.data.result.length > 0) {
        console.log('百度语音识别成功，结果:', response.data.result[0]);
        return response.data.result[0] || '';
      } else if (response.data && response.data.err_no !== 0) {
        console.error('百度语音识别错误码:', response.data.err_no);
        console.error('百度语音识别错误信息:', response.data.err_msg);

        // 根据错误码提供更具体的错误信息和解决方案
        let errorMessage = `百度语音识别错误: ${response.data.err_msg}`;
        let solution = '';

        switch (response.data.err_no) {
          case 3300:
            errorMessage = '语音数据格式错误';
            solution = '请检查音频格式是否为PCM/WAV/AMR，采样率是否为16k，以及base64编码是否正确。';
            break;
          case 3301:
            errorMessage = '识别超时';
            solution = '请确保网络连接稳定，并尝试录制更短的音频。';
            break;
          case 3302:
            errorMessage = '验证失败';
            solution = '请检查API密钥和访问令牌是否正确。';
            break;
          case 3303:
            errorMessage = '语音服务器后端错误';
            solution = '请稍后重试。';
            break;
          case 3304:
            errorMessage = '用户请求超限';
            solution = '请降低请求频率。';
            break;
          case 3305:
            errorMessage = '用户请求频率超限';
            solution = '请降低请求频率。';
            break;
          case 3307:
            errorMessage = '语音识别失败';
            solution = '可能是录音时间太短、音频质量不佳或格式不兼容。请尝试：1) 录制更长时间的音频（至少2-3秒）；2) 在安静环境中录音；3) 确保说话清晰。';
            break;
        }

        console.error(`错误详情: ${errorMessage}`);
        console.error(`解决方案: ${solution}`);

        throw new Error(`${errorMessage}. ${solution}`);
      } else if (!response.data) {
        console.error('百度语音识别返回空数据');
        throw new Error('百度语音识别返回空数据');
      } else {
        console.error('百度语音识别返回未知格式数据:', response.data);
        throw new Error('百度语音识别返回未知格式数据');
      }
    } catch (error) {
      console.error('语音识别错误:', error);

      // 处理不同类型的错误
      if (isAxiosError(error) && error.response) {
        // API错误
        const statusCode = error.response.status;
        const errorData = error.response.data;
        console.error('百度API错误状态码:', statusCode);
        console.error('百度API错误数据:', errorData);

        const errorMessage = errorData?.error_msg || errorData?.err_msg || JSON.stringify(errorData) || 'Unknown API error';
        throw new Error(`百度API错误 (${statusCode}): ${errorMessage}`);
      } else if (error instanceof Error) {
        // 直接抛出原始错误
        throw error;
      }

      // 其他错误
      throw new Error('语音识别失败，请重试');
    }
  }

  /**
   * 使用OpenAI Whisper API进行语音识别（备用方法）
   * @param audioUri 音频文件URI
   * @returns Promise<string> 识别出的文本
   */
  static async recognizeSpeechWithWhisper(audioUri: string): Promise<string> {
    try {
      // 创建FormData对象
      const formData = new FormData();

      // 获取文件名
      const uriParts = audioUri.split('/');
      const fileName = uriParts[uriParts.length - 1];

      // 添加文件
      formData.append('file', {
        uri: audioUri,
        name: fileName,
        type: 'audio/m4a', // 根据实际录音格式调整
      } as any);

      // 添加模型参数
      formData.append('model', 'whisper-1');
      formData.append('language', 'en'); // 指定英语

      // 发送请求
      const response = await axios.post(WHISPER_API_URL, formData, {
        headers: {
          'Authorization': `Bearer sk-placeholder`, // OpenAI功能已禁用
          'Content-Type': 'multipart/form-data',
        },
      });

      // 返回识别结果
      return response.data.text;
    } catch (error) {
      console.error('Whisper API error:', error);
      throw new Error('OpenAI Whisper API 识别失败');
    }
  }

  /**
   * 使用模拟数据进行语音识别（用于测试）
   * @param _audioUri 音频文件URI（实际上不会使用）
   * @returns Promise<string> 模拟的识别文本
   */
  static async recognizeSpeechMock(_audioUri: string): Promise<string> {
    // 模拟API延迟
    await new Promise(resolve => setTimeout(resolve, 2000));

    // 返回模拟数据
    const mockResponses = [
      "Hello, how are you today?",
      "I would like to practice English conversation.",
      "What is your favorite hobby?",
      "The weather is very nice today.",
      "Can you help me with my pronunciation?"
    ];

    return mockResponses[Math.floor(Math.random() * mockResponses.length)];
  }

  /**
   * 测试语音识别功能（用于调试）
   * @param audioUri 音频文件URI
   * @returns Promise<string> 测试结果
   */
  static async testSpeechRecognition(audioUri: string): Promise<string> {
    try {
      console.log('=== 开始语音识别测试 ===');

      // 1. 检查文件
      const fileInfo = await FileSystem.getInfoAsync(audioUri);
      console.log('文件信息:', fileInfo);

      if (!fileInfo.exists) {
        return '测试失败: 音频文件不存在';
      }

      // 2. 检查音频质量（使用改进版本）
      const qualityCheck = await this.checkAudioQualityAdvanced(audioUri);
      console.log('质量检查:', qualityCheck);

      if (!qualityCheck.isValid) {
        return `测试失败: ${qualityCheck.message}`;
      }

      // 3. 测试base64转换
      try {
        const audioData = await this.convertAudioToBase64(audioUri);
        console.log(`Base64转换成功，PCM数据长度: ${audioData.pcmDataLength}字节`);
      } catch (error) {
        return `测试失败: Base64转换错误 - ${error}`;
      }

      // 4. 测试访问令牌
      try {
        const token = await this.getBaiduAccessToken();
        console.log('访问令牌获取成功，长度:', token.length);
      } catch (error) {
        return `测试失败: 访问令牌获取错误 - ${error}`;
      }

      // 5. 尝试实际识别
      try {
        const result = await this.recognizeSpeech(audioUri);
        return `测试成功: ${result}`;
      } catch (error) {
        return `识别失败但测试通过: ${error}`;
      }

    } catch (error) {
      return `测试异常: ${error}`;
    }
  }
}

/**
 * 检查API密钥是否有效
 * @param type API类型
 * @returns Promise<boolean> 是否有效
 */
export async function hasValidApiKey(type: 'deepseek' | 'baidu' = 'baidu'): Promise<boolean> {
  try {
    const config = await ApiConfigService.getApiConfig();

    switch (type) {
      case 'deepseek':
        return config.deepseekApiKey.length > 0 && config.deepseekApiKey.startsWith('sk-');
      case 'baidu':
      default:
        return config.baiduApiKey.length > 0 && config.baiduSecretKey.length > 0 &&
               config.baiduApiKey.length >= 10 && config.baiduSecretKey.length >= 10;
    }
  } catch (error) {
    console.error('检查API密钥有效性失败:', error);
    return false;
  }
}
