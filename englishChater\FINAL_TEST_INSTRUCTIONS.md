# 百度语音识别修复后测试说明

## 🎯 修复完成

百度语音识别模块已经完全修复，解决了关键的PCM数据提取问题。

## ✅ 修复内容总结

### 1. 音频录制格式
- 改为使用 `DEFAULT` 格式产生真正的WAV/PCM格式
- 设置16000Hz采样率、16bit位深、单声道

### 2. PCM数据提取（关键修复）
- 分析WAV文件头，提取纯PCM数据
- 跳过WAV文件头，只发送PCM数据给百度API
- 正确计算len参数（PCM数据字节数）

### 3. API请求参数
- 严格按照百度官方示例设置所有参数
- 使用官方推荐的URL和请求头

## 🧪 测试验证

### API格式测试
运行测试脚本验证API调用格式：
```bash
node scripts/test-baidu-api-fixed.js
```
**结果**: ✅ 成功返回 `err_no: 0, err_msg: "success."`

### PCM数据提取测试
运行PCM提取测试：
```bash
node scripts/test-pcm-extraction.js
```
**结果**: ✅ PCM数据提取成功

## 📱 真实设备测试

### 启动应用
```bash
npx expo start
```

### 测试步骤
1. 在手机上打开Expo Go应用
2. 扫描二维码连接到开发服务器
3. 进入聊天界面
4. 点击录音按钮开始录音
5. 说一段英语（建议2-5秒）
6. 停止录音，观察识别结果

### 预期结果
- 录音成功：显示录音时长
- 语音识别成功：返回识别的英文文本
- 无错误码3300

### 日志检查
在控制台中查看以下关键日志：
```
LOG  检测到WAV文件，提取PCM数据...
LOG  WAV文件头大小: XX字节
LOG  PCM数据长度: XX字节
LOG  百度API官方参数: format: "pcm", rate: 16000, len: XX
LOG  百度语音识别API响应: {"err_no": 0, "err_msg": "success.", "result": [...]}
```

## 🔧 如果仍有问题

### 检查API密钥
确保在 `services/api.ts` 中设置了正确的百度API密钥：
```typescript
export const BAIDU_API_KEY = '你的API密钥';
export const BAIDU_SECRET_KEY = '你的Secret密钥';
```

### 检查网络连接
确保设备能够访问百度API服务器。

### 检查录音权限
确保应用有录音权限。

## 📋 技术细节

### WAV文件结构
```
[WAV文件头 44字节] + [PCM数据 N字节]
```

### 修复前后对比
- **修复前**: 发送完整WAV文件（包含文件头）→ 错误码3300
- **修复后**: 只发送PCM数据部分 → 成功识别

### 关键参数
- `format`: "pcm" (百度推荐)
- `rate`: 16000 (百度推荐)
- `channel`: 1 (百度要求)
- `len`: PCM数据字节数（不包含WAV文件头）
- `speech`: 纯PCM数据的base64编码

## 🎉 预期效果

修复后，语音识别应该能够：
1. 正确录制音频
2. 成功提取PCM数据
3. 正确调用百度API
4. 返回准确的识别结果

现在可以正常使用英语口语练习功能了！
