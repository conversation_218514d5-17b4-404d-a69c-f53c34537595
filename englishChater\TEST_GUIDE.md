# 语音识别修复测试指南

## 🚀 应用已启动

应用正在运行在端口 8082：
- **Expo Go**: 扫描二维码或访问 `exp://************:8082`
- **Web版本**: 访问 `http://localhost:8082`

## 📱 测试步骤

### 1. 打开应用
- 使用Expo Go扫描二维码，或在浏览器中打开web版本
- 进入聊天界面

### 2. 进行语音识别测试
1. **点击测试按钮**：右上角的扳手图标 🔧
2. **录制音频**：
   - 点击"确定"开始录音
   - 清晰地说一句英语（建议2-5秒）
   - 例如："Hello, how are you today?"
3. **查看结果**：等待测试完成并查看弹窗结果

### 3. 观察日志输出
在控制台中查看详细的调试信息，重点关注：

#### ✅ 期望看到的成功日志：
```
LOG  开始录音，强制使用WAV/PCM格式...
LOG  录音配置: {"android": {"outputFormat": "DEFAULT (WAV)", "audioEncoder": "DEFAULT (PCM)"...}}
LOG  WAV文件分析: 分析前XXX字节
LOG  文件签名: RIFF="RIFF", WAVE="WAVE"
LOG  找到data chunk: 大小=XXX字节, 文件头大小=XXX字节
LOG  使用WAV文件头分析结果
LOG  最终音频格式: wav
LOG  百度语音识别API响应: {"result": ["识别的文本"]}
```

#### ❌ 需要关注的错误：
- `err_no: 3300` - 语音数据格式错误（应该已修复）
- `不是标准WAV文件格式` - 音频格式问题
- `Base64格式验证失败` - 编码问题

## 🔧 主要修复验证点

### 1. 音频录制格式
- ✅ 使用 `DEFAULT` 输出格式和编码器
- ✅ 16000Hz采样率，单声道
- ✅ 生成真正的WAV文件

### 2. len参数计算
- ✅ 优先使用WAV文件头分析结果
- ✅ 文件大小比率合理（实际/理论 ≈ 0.8-1.2）
- ✅ 回退策略正常工作

### 3. base64编码
- ✅ 移除所有前缀和空白字符
- ✅ 通过格式验证
- ✅ 纯净的base64编码

### 4. API参数
- ✅ 设备唯一标识符生成
- ✅ 所有必填参数正确
- ✅ 格式符合百度API要求

## 📊 测试结果判断

### 🎉 成功标志：
1. **录音成功**：能够正常录制音频
2. **格式识别**：WAV文件头分析成功
3. **API调用**：返回状态码200
4. **识别结果**：返回识别的英文文本

### ⚠️ 需要进一步调试：
1. **3300错误**：检查len参数和base64编码
2. **3302错误**：检查API密钥配置
3. **3307错误**：改善录音环境和质量

## 🛠️ 故障排除

### 如果仍然出现3300错误：
1. 检查录音配置是否正确应用
2. 查看WAV文件分析结果
3. 验证len参数计算逻辑
4. 检查base64编码是否纯净

### 如果录音格式不正确：
1. 确认使用的是DEFAULT格式
2. 检查设备兼容性
3. 尝试重启应用

### 如果API调用失败：
1. 验证网络连接
2. 检查API密钥有效性
3. 确认访问令牌获取成功

## 📝 测试记录模板

```
测试时间：____
设备类型：____
录音时长：____秒
文件大小：____字节
WAV分析：成功/失败
API响应：成功/失败 (错误码：____)
识别结果：____
```

## 🔄 如果需要重新测试

1. **重启应用**：在控制台按 `r` 重新加载
2. **清除缓存**：删除录音文件缓存
3. **检查代码**：确认修复已正确应用

---

**注意**：如果测试成功，说明语音识别模块修复有效。如果仍有问题，请提供详细的日志输出以便进一步诊断。
