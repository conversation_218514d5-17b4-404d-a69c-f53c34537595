# 语音识别修复验证清单

## 修复内容总结

### 1. 音频录制格式优化
- **问题**: 使用 `AndroidOutputFormat.DEFAULT` 可能不产生标准PCM格式
- **修复**: 改为使用 `AndroidOutputFormat.MPEG_4` + `AndroidAudioEncoder.AAC` 确保兼容性
- **影响**: 提高音频格式的标准化程度

### 2. len参数计算修复（关键修复）
- **问题**: len参数计算不准确，可能导致百度API返回3300错误
- **修复**: 
  - 优先使用WAV文件头分析结果
  - 其次使用理论PCM长度计算：`时长(秒) × 16000Hz × 2字节 × 1声道`
  - 最后回退到 `解码长度 - 44字节文件头`
- **影响**: 确保len参数符合百度API要求

### 3. base64编码处理增强
- **问题**: 可能包含data URL前缀或空白字符
- **修复**: 
  - 移除所有可能的前缀格式
  - 移除空白字符
  - 增强base64格式验证
- **影响**: 确保speech参数是纯净的base64编码

### 4. CUID参数优化
- **问题**: 使用固定字符串作为用户标识
- **修复**: 生成并持久化设备唯一标识符
- **影响**: 符合百度API的用户标识要求

### 5. 错误处理和调试信息增强
- **修复**: 添加详细的参数日志输出，便于调试
- **影响**: 更容易定位问题

## 验证步骤

1. **启动应用**: `npx expo start`
2. **进入聊天界面**: 选择或创建对话
3. **点击测试按钮**: 使用右上角的扳手图标
4. **录制测试音频**: 说一句清晰的英语（2-5秒）
5. **查看测试结果**: 检查是否成功识别

## 预期结果

- ✅ 音频录制成功
- ✅ base64转换成功
- ✅ 访问令牌获取成功
- ✅ 百度API调用成功
- ✅ 返回识别文本

## 常见错误码及解决方案

### 3300 - 语音数据格式错误
- **原因**: len参数不正确或base64编码有问题
- **解决**: 已通过修复len计算和base64处理解决

### 3302 - 验证失败
- **原因**: API密钥或访问令牌无效
- **解决**: 检查BAIDU_API_KEY和BAIDU_SECRET_KEY

### 3307 - 语音识别失败
- **原因**: 音频质量不佳或时长太短
- **解决**: 录制更长时间的清晰音频

## 技术细节

### 百度API要求规范
- **URL**: `http://vop.baidu.com/server_api`
- **Method**: POST
- **Content-Type**: application/json
- **格式**: PCM/WAV
- **采样率**: 16000Hz（固定）
- **声道**: 1（单声道，固定）
- **位深**: 16bit

### 关键参数
```json
{
  "format": "pcm",
  "rate": 16000,
  "channel": 1,
  "token": "有效的access_token",
  "cuid": "设备唯一标识",
  "speech": "纯净的base64编码",
  "len": "原始音频数据字节数",
  "dev_pid": 1737
}
```

### len参数计算公式
```
对于PCM格式：
len = 音频时长(秒) × 采样率(16000) × 位深(2字节) × 声道数(1)
例如：3秒音频 = 3 × 16000 × 2 × 1 = 96000字节
```
