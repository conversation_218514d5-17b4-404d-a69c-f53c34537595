/**
 * 百度语音识别API测试脚本
 * 用于验证API配置和参数是否正确
 */

const axios = require('axios');

// 百度API配置
const BAIDU_API_KEY = '4SJ6Uw96rKEEyeiuXLgrpaBi';
const BAIDU_SECRET_KEY = 'WzrJdgyeL9iVBgFIgFqysuFcntzyQA5g';

/**
 * 获取百度访问令牌
 */
async function getBaiduAccessToken() {
  try {
    console.log('正在获取百度访问令牌...');
    
    const url = `https://aip.baidubce.com/oauth/2.0/token?grant_type=client_credentials&client_id=${BAIDU_API_KEY}&client_secret=${BAIDU_SECRET_KEY}`;
    
    const response = await axios.post(url, null, {
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    });
    
    if (response.data && response.data.access_token) {
      console.log('✅ 成功获取访问令牌');
      console.log('令牌长度:', response.data.access_token.length);
      console.log('过期时间:', response.data.expires_in, '秒');
      return response.data.access_token;
    } else {
      console.error('❌ 访问令牌响应格式错误:', response.data);
      return null;
    }
  } catch (error) {
    console.error('❌ 获取访问令牌失败:', error.message);
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', error.response.data);
    }
    return null;
  }
}

/**
 * 测试百度语音识别API参数格式
 */
async function testApiParameterFormat() {
  console.log('\n=== 测试API参数格式 ===');
  
  const token = await getBaiduAccessToken();
  if (!token) {
    console.error('❌ 无法获取访问令牌，跳过参数测试');
    return;
  }
  
  // 模拟音频参数
  const mockAudioDuration = 3; // 3秒
  const theoreticalPcmLength = mockAudioDuration * 16000 * 2 * 1; // 96000字节
  const mockBase64Length = Math.ceil(theoreticalPcmLength * 4 / 3); // base64编码后长度
  
  console.log('模拟音频参数:');
  console.log('- 时长:', mockAudioDuration, '秒');
  console.log('- 理论PCM长度:', theoreticalPcmLength, '字节');
  console.log('- Base64编码长度:', mockBase64Length, '字符');
  
  // 构建请求参数
  const requestData = {
    format: 'pcm',
    rate: 16000,
    channel: 1,
    token: token,
    cuid: `test_device_${Date.now()}`,
    speech: 'VGVzdCBhdWRpbyBkYXRh', // 测试用的base64数据
    len: theoreticalPcmLength,
    dev_pid: 1737
  };
  
  console.log('\n请求参数验证:');
  console.log('✅ format:', requestData.format);
  console.log('✅ rate:', requestData.rate);
  console.log('✅ channel:', requestData.channel);
  console.log('✅ token长度:', requestData.token.length);
  console.log('✅ cuid:', requestData.cuid);
  console.log('✅ speech长度:', requestData.speech.length);
  console.log('✅ len:', requestData.len);
  console.log('✅ dev_pid:', requestData.dev_pid);
  
  // 验证参数类型
  const validations = [
    { name: 'format', value: requestData.format, type: 'string', expected: ['pcm', 'wav', 'amr'] },
    { name: 'rate', value: requestData.rate, type: 'number', expected: [16000] },
    { name: 'channel', value: requestData.channel, type: 'number', expected: [1] },
    { name: 'len', value: requestData.len, type: 'number', min: 1 },
    { name: 'dev_pid', value: requestData.dev_pid, type: 'number', expected: [1737] }
  ];
  
  console.log('\n参数类型验证:');
  let allValid = true;
  
  for (const validation of validations) {
    const isValidType = typeof validation.value === validation.type;
    const isValidValue = validation.expected ? 
      validation.expected.includes(validation.value) : 
      (validation.min ? validation.value >= validation.min : true);
    
    const isValid = isValidType && isValidValue;
    allValid = allValid && isValid;
    
    console.log(
      isValid ? '✅' : '❌', 
      validation.name + ':', 
      validation.value,
      isValid ? '(有效)' : '(无效)'
    );
  }
  
  if (allValid) {
    console.log('\n🎉 所有参数验证通过！');
  } else {
    console.log('\n❌ 参数验证失败，请检查配置');
  }
}

/**
 * 主测试函数
 */
async function main() {
  console.log('🚀 开始百度语音识别API测试\n');
  
  // 检查API密钥
  console.log('=== 检查API配置 ===');
  console.log('API Key:', BAIDU_API_KEY ? '✅ 已配置' : '❌ 未配置');
  console.log('Secret Key:', BAIDU_SECRET_KEY ? '✅ 已配置' : '❌ 未配置');
  
  if (!BAIDU_API_KEY || !BAIDU_SECRET_KEY) {
    console.error('❌ API密钥未正确配置');
    return;
  }
  
  // 测试访问令牌获取
  await getBaiduAccessToken();
  
  // 测试API参数格式
  await testApiParameterFormat();
  
  console.log('\n✨ 测试完成');
}

// 运行测试
main().catch(console.error);
